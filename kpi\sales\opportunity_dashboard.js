// Dashboard component for Sales KPI Dashboard
import { connectionManager } from '../../core/connection.js';
import { NotificationSystem } from '../../core/notifications.js';

export class OpportunityDashboard {
  constructor(container, opportunityComponent) {
    this.container = container;
    this.opportunityComponent = opportunityComponent;
    this.dashboard = null;
    this.isLoading = true;
    this.notificationSystem = new NotificationSystem();
    this.renderHeader = false; // Default to false - header handled by parent component
    this.dateRange = null; // Date range for filtering
    this.filterStatus = 'all'; // Status filter
    this.selectedSalesRep = 'all'; // Selected sales rep filter
    this.charts = {}; // Store chart instances
    this.apexChartsLoaded = false;
  }

  async init() {
    console.log("Initializing Opportunity Dashboard component");
    this.isLoading = true;
    this.render();
    
    try {
      // Load ApexCharts library
      await this.loadApexChartsLibrary();
      
      // Calculate dashboard metrics from opportunity data
      await this.calculateDashboard();
      
      this.isLoading = false;
      this.render();
      
      // Set up event listeners and ensure initial dropdown state
      this.setupEventListeners();
      this.ensureDropdownState();
      
    } catch (error) {
      console.error("Error initializing opportunity dashboard:", error);
      this.isLoading = false;
      this.showError("Failed to initialize dashboard: " + error.message);
      this.render();
    }
  }

  async loadApexChartsLibrary() {
    if (this.apexChartsLoaded || window.ApexCharts) {
      this.apexChartsLoaded = true;
      return;
    }

    try {
      // Try to load from local library first
      if (!window.ApexCharts) {
        const script = document.createElement('script');
        script.src = '/kpi/library/apexcharts.min.js';
        script.async = true;
        
        await new Promise((resolve, reject) => {
          script.onload = () => {
            this.apexChartsLoaded = true;
            resolve();
          };
          script.onerror = () => {
            // Fallback to CDN
            const cdnScript = document.createElement('script');
            cdnScript.src = 'https://cdn.jsdelivr.net/npm/apexcharts@latest';
            cdnScript.async = true;
            cdnScript.onload = () => {
              this.apexChartsLoaded = true;
              resolve();
            };
            cdnScript.onerror = reject;
            document.head.appendChild(cdnScript);
          };
          document.head.appendChild(script);
        });
      } else {
        this.apexChartsLoaded = true;
      }
    } catch (error) {
      console.error("Error loading ApexCharts:", error);
      throw new Error("Failed to load ApexCharts library");
    }
  }

  async calculateDashboard() {
    // Get opportunities from the main component
    const opportunities = this.opportunityComponent.opportunities;

    if (!opportunities || opportunities.length === 0) {
      throw new Error("No opportunity data available");
    }

    // Get filtered opportunities based on filters
    let filteredOpportunities = [...opportunities];

    // Apply date range filter if specified
    if (this.dateRange && this.dateRange.start && this.dateRange.end) {
      const startDate = new Date(this.dateRange.start);
      startDate.setHours(0, 0, 0, 0);
      const endDate = new Date(this.dateRange.end);
      endDate.setHours(23, 59, 59, 999);

      filteredOpportunities = filteredOpportunities.filter(opp => {
        let dateToCheck = null;
        // Always prioritize CreatedDate for consistency with main component
        if (opp.ODataInfo?.CreatedDate) {
          dateToCheck = new Date(opp.ODataInfo.CreatedDate);
        } else if (opp.LastModified instanceof Date) {
          // Fall back to LastModified if CreatedDate not available
          dateToCheck = opp.LastModified;
        }

        if (dateToCheck && !isNaN(dateToCheck.getTime())) {
          return dateToCheck >= startDate && dateToCheck <= endDate;
        }
        return false;
      });
    }

    // Apply status filter if specified and not 'all'
    if (this.filterStatus && this.filterStatus !== 'all') {
      filteredOpportunities = filteredOpportunities.filter(opp => 
        opp.Status?.toLowerCase() === this.filterStatus.toLowerCase()
      );
    }

    // Apply sales rep filter if specified and not 'all'
    if (this.selectedSalesRep && this.selectedSalesRep !== 'all') {
      const beforeFilterCount = filteredOpportunities.length;
      filteredOpportunities = filteredOpportunities.filter(opp => 
        opp.OwnerEmployeeName === this.selectedSalesRep
      );
      console.log(`Sales Rep Filter Applied: "${this.selectedSalesRep}" - ${beforeFilterCount} -> ${filteredOpportunities.length} opportunities`);
    } else {
      console.log(`Sales Rep Filter: "all" - ${filteredOpportunities.length} opportunities (no filtering)`);
    }

    // Initialize dashboard structure
    this.dashboard = {
      summary: {
        totalOpportunities: filteredOpportunities.length,
        totalValue: 0,
        avgOpportunityValue: 0,
        winRate: 0,
        avgSalesCycle: 0
      },
      salesRepPerformance: {},
      monthlyTrends: {
        opportunities: [],
        values: [],
        winRates: [],
        months: []
      },
      topPerformers: {
        byRevenue: [],
        byOpportunities: [],
        byWinRate: []
      },
      salesRepList: []
    };

    // Calculate summary metrics
    if (filteredOpportunities.length > 0) {
      this.dashboard.summary.totalValue = filteredOpportunities.reduce((sum, opp) => 
        sum + (parseFloat(opp.Amount) || 0), 0);
      
      this.dashboard.summary.avgOpportunityValue = 
        this.dashboard.summary.totalValue / filteredOpportunities.length;

      // Calculate win rate
      const closedOpps = filteredOpportunities.filter(opp => 
        opp.Status && ['won', 'lost'].includes(opp.Status.toLowerCase()));
      const wonOpps = filteredOpportunities.filter(opp => 
        opp.Status && opp.Status.toLowerCase() === 'won');
      
      if (closedOpps.length > 0) {
        this.dashboard.summary.winRate = (wonOpps.length / closedOpps.length) * 100;
      }

      // Calculate average sales cycle
      const cycleData = filteredOpportunities
        .filter(opp => this.opportunityComponent.calculateSalesCycle(opp) !== 'N/A')
        .map(opp => parseInt(this.opportunityComponent.calculateSalesCycle(opp)));
      
      if (cycleData.length > 0) {
        this.dashboard.summary.avgSalesCycle = 
          cycleData.reduce((sum, days) => sum + days, 0) / cycleData.length;
      }
    }

    // Get all sales reps from original opportunities (not filtered) for dropdown
    const allSalesReps = new Set();
    this.opportunityComponent.opportunities.forEach(opp => {
      if (opp.OwnerEmployeeName) {
        allSalesReps.add(opp.OwnerEmployeeName);
      }
    });

    // Calculate sales rep performance from filtered opportunities
    const repPerformance = {};

    filteredOpportunities.forEach(opp => {
      if (!opp.OwnerEmployeeName) return;
      
      const rep = opp.OwnerEmployeeName;
      
      if (!repPerformance[rep]) {
        repPerformance[rep] = {
          name: rep,
          totalOpportunities: 0,
          totalValue: 0,
          won: 0,
          lost: 0,
          pending: 0,
          avgOpportunityValue: 0,
          winRate: 0,
          avgSalesCycle: 0,
          cycleDays: []
        };
      }
      
      repPerformance[rep].totalOpportunities++;
      repPerformance[rep].totalValue += parseFloat(opp.Amount) || 0;
      
      if (opp.Status && opp.Status.toLowerCase() === 'won') {
        repPerformance[rep].won++;
      } else if (opp.Status && opp.Status.toLowerCase() === 'lost') {
        repPerformance[rep].lost++;
      } else {
        repPerformance[rep].pending++;
      }

      // Calculate sales cycle for this rep
      const cycle = this.opportunityComponent.calculateSalesCycle(opp);
      if (cycle !== 'N/A') {
        repPerformance[rep].cycleDays.push(parseInt(cycle));
      }
    });

    // Calculate derived metrics for each rep
    Object.values(repPerformance).forEach(rep => {
      rep.avgOpportunityValue = rep.totalOpportunities > 0 ? 
        rep.totalValue / rep.totalOpportunities : 0;
      
      const closedDeals = rep.won + rep.lost;
      rep.winRate = closedDeals > 0 ? (rep.won / closedDeals) * 100 : 0;
      
      rep.avgSalesCycle = rep.cycleDays.length > 0 ?
        rep.cycleDays.reduce((sum, days) => sum + days, 0) / rep.cycleDays.length : 0;
    });

    this.dashboard.salesRepPerformance = repPerformance;
    this.dashboard.salesRepList = Array.from(allSalesReps).sort();

    // Calculate top performers
    const reps = Object.values(repPerformance);
    
    this.dashboard.topPerformers.byRevenue = reps
      .sort((a, b) => b.totalValue - a.totalValue)
      .slice(0, 5);
    
    this.dashboard.topPerformers.byOpportunities = reps
      .sort((a, b) => b.totalOpportunities - a.totalOpportunities)
      .slice(0, 5);
    
    this.dashboard.topPerformers.byWinRate = reps
      .filter(rep => rep.won + rep.lost > 0) // Only reps with closed deals
      .sort((a, b) => b.winRate - a.winRate)
      .slice(0, 5);

    // Calculate monthly trends
    const monthlyData = {};
    filteredOpportunities.forEach(opp => {
      let dateToUse = null;
      
      if (opp.ODataInfo?.CreatedDate) {
        dateToUse = new Date(opp.ODataInfo.CreatedDate);
      } else if (opp.LastModified instanceof Date) {
        dateToUse = opp.LastModified;
      }
      
      if (dateToUse && !isNaN(dateToUse.getTime())) {
        const monthKey = `${dateToUse.getFullYear()}-${String(dateToUse.getMonth() + 1).padStart(2, '0')}`;
        
        if (!monthlyData[monthKey]) {
          monthlyData[monthKey] = {
            opportunities: 0,
            value: 0,
            won: 0,
            lost: 0
          };
        }
        
        monthlyData[monthKey].opportunities++;
        monthlyData[monthKey].value += parseFloat(opp.Amount) || 0;
        
        if (opp.Status && opp.Status.toLowerCase() === 'won') {
          monthlyData[monthKey].won++;
        } else if (opp.Status && opp.Status.toLowerCase() === 'lost') {
          monthlyData[monthKey].lost++;
        }
      }
    });

    // Convert to arrays for charts
    const sortedMonths = Object.keys(monthlyData).sort();
    this.dashboard.monthlyTrends.months = sortedMonths.map(month => {
      const [year, monthNum] = month.split('-');
      const date = new Date(parseInt(year), parseInt(monthNum) - 1);
      return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    });
    
    this.dashboard.monthlyTrends.opportunities = sortedMonths.map(month => 
      monthlyData[month].opportunities);
    
    this.dashboard.monthlyTrends.values = sortedMonths.map(month => 
      monthlyData[month].value);
    
    this.dashboard.monthlyTrends.winRates = sortedMonths.map(month => {
      const data = monthlyData[month];
      const closedDeals = data.won + data.lost;
      return closedDeals > 0 ? (data.won / closedDeals) * 100 : 0;
    });

    console.log("Dashboard calculated:", this.dashboard);
    return this.dashboard;
  }

  formatCurrency(value) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(value);
  }

  formatNumber(value, decimals = 0) {
    return new Intl.NumberFormat('en-US', {
      maximumFractionDigits: decimals
    }).format(value);
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderDashboard();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading sales dashboard...</p>
      </div>
    `;
  }

  renderDashboard() {
    if (!this.dashboard) {
      this.container.innerHTML = `
        <p class="text-center text-gray-500 dark:text-gray-400">No dashboard data available</p>
      `;
      return;
    }

    const { summary, salesRepPerformance, topPerformers, salesRepList } = this.dashboard;

    // Create header content - only included if renderHeader is true
    let headerContent = '';
    if (this.renderHeader) {
      headerContent = `
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Sales Dashboard</h2>
        </div>
      `;
    }

    this.container.innerHTML = `
      ${headerContent}

      <!-- Sales Rep Selector -->
      <div class="flex justify-between items-center mb-6">
        <div class="flex items-center">
          <label class="text-sm text-gray-600 dark:text-gray-400 mr-3">Sales Rep:</label>
          <select id="sales-rep-selector" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm">
            <option value="all" ${this.selectedSalesRep === 'all' ? 'selected' : ''}>All Sales Reps</option>
            ${salesRepList.map(rep => `
              <option value="${rep}" ${this.selectedSalesRep === rep ? 'selected' : ''}>${rep}</option>
            `).join('')}
          </select>
        </div>
        
        ${this.selectedSalesRep !== 'all' ? `
          <div class="text-sm text-gray-600 dark:text-gray-400">
            Showing performance for: <span class="font-medium text-blue-600 dark:text-blue-400">${this.selectedSalesRep}</span>
          </div>
        ` : ''}
      </div>

      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        <!-- Total Opportunities Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <div class="flex items-center">
            <div class="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-lg mr-3">
              <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
            </div>
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">Total Opportunities</p>
              <h3 class="text-2xl font-bold text-gray-800 dark:text-white">${summary.totalOpportunities}</h3>
            </div>
          </div>
        </div>

        <!-- Total Value Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <div class="flex items-center">
            <div class="p-2 bg-green-50 dark:bg-green-900/30 rounded-lg mr-3">
              <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">Total Value</p>
              <h3 class="text-2xl font-bold text-gray-800 dark:text-white">${this.formatCurrency(summary.totalValue)}</h3>
            </div>
          </div>
        </div>

        <!-- Avg Opportunity Value Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <div class="flex items-center">
            <div class="p-2 bg-purple-50 dark:bg-purple-900/30 rounded-lg mr-3">
              <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">Avg Value</p>
              <h3 class="text-2xl font-bold text-gray-800 dark:text-white">${this.formatCurrency(summary.avgOpportunityValue)}</h3>
            </div>
          </div>
        </div>

        <!-- Win Rate Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-50 dark:bg-yellow-900/30 rounded-lg mr-3">
              <svg class="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
              </svg>
            </div>
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">Win Rate</p>
              <h3 class="text-2xl font-bold text-gray-800 dark:text-white">${this.formatNumber(summary.winRate, 1)}%</h3>
            </div>
          </div>
        </div>

        <!-- Avg Sales Cycle Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <div class="flex items-center">
            <div class="p-2 bg-red-50 dark:bg-red-900/30 rounded-lg mr-3">
              <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div>
              <p class="text-sm text-gray-500 dark:text-gray-400">Avg Cycle</p>
              <h3 class="text-2xl font-bold text-gray-800 dark:text-white">${this.formatNumber(summary.avgSalesCycle, 0)}d</h3>
            </div>
          </div>
        </div>
      </div>

      <!-- Charts Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Monthly Trends Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">Monthly Performance Trends</h3>
          <div id="monthly-trends-chart" style="height: 300px;"></div>
        </div>

        <!-- Sales Rep Performance Chart -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">Sales Rep Comparison</h3>
          <div id="sales-rep-chart" style="height: 300px;"></div>
        </div>
      </div>

      <!-- Detailed Analytics Section -->
      ${this.selectedSalesRep !== 'all' ? `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4 mb-6">
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">Detailed Analysis: ${this.selectedSalesRep}</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            ${this.renderSalesRepAnalysis(this.selectedSalesRep)}
          </div>
        </div>
      ` : ''}

      <!-- Pipeline Analysis -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">Pipeline Analysis</h3>
          <div class="space-y-4">
            ${this.renderPipelineAnalysis()}
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">Forecast Analysis</h3>
          <div class="space-y-4">
            ${this.renderForecastAnalysis()}
          </div>
        </div>
      </div>

      <!-- Recent Activities -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4 mb-6">
        <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">Recent Activities & Key Opportunities</h3>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h4 class="font-medium text-gray-600 dark:text-gray-400 mb-3">Recent High-Value Opportunities</h4>
            ${this.renderRecentOpportunities()}
          </div>
          <div>
            <h4 class="font-medium text-gray-600 dark:text-gray-400 mb-3">Opportunities Closing Soon</h4>
            ${this.renderClosingSoonOpportunities()}
          </div>
        </div>
      </div>

      <!-- Top Performers Section -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Top by Revenue -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">Top by Revenue</h3>
          <div class="space-y-3">
            ${topPerformers.byRevenue.slice(0, 5).map((rep, index) => `
              <div class="flex items-center justify-between p-2 rounded ${index === 0 ? 'bg-yellow-50 dark:bg-yellow-900/20' : 'bg-gray-50 dark:bg-gray-900'} hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer sales-rep-detail-btn" data-rep-name="${rep.name}">
                <div class="flex items-center">
                  <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center mr-3">
                    <span class="text-sm font-bold text-blue-600 dark:text-blue-300">${index + 1}</span>
                  </div>
                  <div>
                    <p class="font-medium text-gray-800 dark:text-gray-200">${rep.name}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">${rep.totalOpportunities} opportunities</p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="font-bold text-gray-800 dark:text-gray-200">${this.formatCurrency(rep.totalValue)}</p>
                  <p class="text-xs text-blue-600 dark:text-blue-400">Click for details</p>
                </div>
              </div>
            `).join('')}
          </div>
        </div>

        <!-- Top by Opportunities -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">Top by Volume</h3>
          <div class="space-y-3">
            ${topPerformers.byOpportunities.slice(0, 5).map((rep, index) => `
              <div class="flex items-center justify-between p-2 rounded ${index === 0 ? 'bg-green-50 dark:bg-green-900/20' : 'bg-gray-50 dark:bg-gray-900'} hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer sales-rep-detail-btn" data-rep-name="${rep.name}">
                <div class="flex items-center">
                  <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-800 flex items-center justify-center mr-3">
                    <span class="text-sm font-bold text-green-600 dark:text-green-300">${index + 1}</span>
                  </div>
                  <div>
                    <p class="font-medium text-gray-800 dark:text-gray-200">${rep.name}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">${this.formatCurrency(rep.totalValue)}</p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="font-bold text-gray-800 dark:text-gray-200">${rep.totalOpportunities}</p>
                  <p class="text-xs text-blue-600 dark:text-blue-400">Click for details</p>
                </div>
              </div>
            `).join('')}
          </div>
        </div>

        <!-- Top by Win Rate -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
          <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">Top by Win Rate</h3>
          <div class="space-y-3">
            ${topPerformers.byWinRate.slice(0, 5).map((rep, index) => `
              <div class="flex items-center justify-between p-2 rounded ${index === 0 ? 'bg-purple-50 dark:bg-purple-900/20' : 'bg-gray-50 dark:bg-gray-900'} hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer sales-rep-detail-btn" data-rep-name="${rep.name}">
                <div class="flex items-center">
                  <div class="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-800 flex items-center justify-center mr-3">
                    <span class="text-sm font-bold text-purple-600 dark:text-purple-300">${index + 1}</span>
                  </div>
                  <div>
                    <p class="font-medium text-gray-800 dark:text-gray-200">${rep.name}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">${rep.won}W / ${rep.lost}L</p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="font-bold text-gray-800 dark:text-gray-200">${this.formatNumber(rep.winRate, 1)}%</p>
                  <p class="text-xs text-blue-600 dark:text-blue-400">Click for details</p>
                </div>
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    `;

    // Render charts after DOM is ready and ensure dropdown state
    setTimeout(() => {
      this.renderCharts();
      this.ensureDropdownState();
    }, 100);
  }

  renderCharts() {
    if (!this.apexChartsLoaded || !window.ApexCharts) {
      console.warn("ApexCharts not loaded, skipping chart rendering");
      return;
    }

    // Destroy existing charts
    Object.values(this.charts).forEach(chart => {
      if (chart && chart.destroy) {
        chart.destroy();
      }
    });
    this.charts = {};

    this.renderMonthlyTrendsChart();
    this.renderSalesRepChart();
  }

  ensureDropdownState() {
    // Ensure the sales rep dropdown shows the correct selected value
    const salesRepSelector = document.getElementById('sales-rep-selector');
    if (salesRepSelector && this.selectedSalesRep) {
      // Verify the selected value exists in the dropdown options
      const option = salesRepSelector.querySelector(`option[value="${this.selectedSalesRep}"]`);
      if (option) {
        salesRepSelector.value = this.selectedSalesRep;
        console.log('Dropdown state ensured for:', this.selectedSalesRep);
      } else {
        console.warn('Selected sales rep not found in dropdown options, resetting to "all"');
        this.selectedSalesRep = 'all';
        salesRepSelector.value = 'all';
      }
    }
  }

  renderMonthlyTrendsChart() {
    const container = document.getElementById('monthly-trends-chart');
    if (!container || !this.dashboard.monthlyTrends.months.length) return;

    const options = {
      series: [
        {
          name: 'Opportunities',
          type: 'column',
          data: this.dashboard.monthlyTrends.opportunities
        },
        {
          name: 'Revenue',
          type: 'line',
          data: this.dashboard.monthlyTrends.values
        },
        {
          name: 'Win Rate (%)',
          type: 'line',
          data: this.dashboard.monthlyTrends.winRates
        }
      ],
      chart: {
        height: 300,
        type: 'line',
        toolbar: { show: false },
        background: 'transparent'
      },
      colors: ['#3B82F6', '#10B981', '#F59E0B'], // Blue, Green, Amber to match other charts
      stroke: {
        width: [0, 3, 3],
        curve: 'smooth'
      },
      plotOptions: {
        bar: {
          columnWidth: '50%'
        }
      },
      fill: {
        opacity: [0.85, 1, 1],
        gradient: {
          inverseColors: false,
          shade: 'light',
          type: "vertical",
          opacityFrom: 0.85,
          opacityTo: 0.55,
          stops: [0, 100, 100, 100]
        }
      },
      labels: this.dashboard.monthlyTrends.months,
      markers: {
        size: 0
      },
      xaxis: {
        type: 'category',
        labels: {
          style: {
            colors: '#6B7280'
          }
        }
      },
      yaxis: [
        {
          title: {
            text: 'Opportunities',
            style: {
              color: '#6B7280'
            }
          },
          labels: {
            style: {
              colors: '#6B7280'
            }
          }
        },
        {
          opposite: true,
          title: {
            text: 'Revenue ($)',
            style: {
              color: '#6B7280'
            }
          },
          labels: {
            style: {
              colors: '#6B7280'
            },
            formatter: (value) => this.formatCurrency(value)
          }
        }
      ],
      tooltip: {
        shared: true,
        intersect: false,
        y: [
          {
            formatter: (y) => y ? `${y} opportunities` : '0 opportunities'
          },
          {
            formatter: (y) => y ? this.formatCurrency(y) : '$0'
          },
          {
            formatter: (y) => y ? `${y.toFixed(1)}%` : '0%'
          }
        ]
      },
      legend: {
        labels: {
          colors: '#6B7280'
        }
      },
      grid: {
        borderColor: '#E5E7EB'
      }
    };

    this.charts.monthlyTrends = new ApexCharts(container, options);
    this.charts.monthlyTrends.render();
  }

  renderSalesRepChart() {
    const container = document.getElementById('sales-rep-chart');
    if (!container || !this.dashboard.salesRepPerformance) return;

    const reps = Object.values(this.dashboard.salesRepPerformance)
      .sort((a, b) => b.totalValue - a.totalValue)
      .slice(0, 10); // Top 10 reps

    if (reps.length === 0) return;

    const options = {
      series: [
        {
          name: 'Revenue',
          type: 'column',
          data: reps.map(rep => rep.totalValue)
        },
        {
          name: 'Win Rate (%)',
          type: 'line',
          data: reps.map(rep => rep.winRate)
        }
      ],
      chart: {
        height: 300,
        type: 'line',
        toolbar: { show: false },
        background: 'transparent'
      },
      colors: ['#3B82F6', '#10B981'], // Match other charts
      stroke: {
        width: [0, 3],
        curve: 'smooth'
      },
      plotOptions: {
        bar: {
          columnWidth: '50%',
          borderRadius: 4
        }
      },
      fill: {
        opacity: [0.85, 1],
        gradient: {
          inverseColors: false,
          shade: 'light',
          type: "vertical",
          opacityFrom: 0.85,
          opacityTo: 0.55,
          stops: [0, 100, 100, 100]
        }
      },
      dataLabels: {
        enabled: false
      },
      markers: {
        size: [0, 4],
        strokeWidth: 2,
        hover: {
          size: 6
        }
      },
      xaxis: {
        categories: reps.map(rep => {
          // Show full name if short, otherwise use initials
          const fullName = rep.name;
          return fullName.length <= 12 ? fullName : fullName.split(' ').map(n => n.charAt(0)).join('');
        }),
        labels: {
          style: {
            colors: '#6B7280'
          },
          rotate: -45,
          rotateAlways: false
        }
      },
      yaxis: [
        {
          title: {
            text: 'Revenue ($)',
            style: {
              color: '#6B7280'
            }
          },
          labels: {
            style: {
              colors: '#6B7280'
            },
            formatter: (value) => this.formatCurrency(value)
          }
        },
        {
          opposite: true,
          title: {
            text: 'Win Rate (%)',
            style: {
              color: '#6B7280'
            }
          },
          labels: {
            style: {
              colors: '#6B7280'
            }
          },
          max: 100
        }
      ],
      tooltip: {
        shared: true,
        intersect: false,
        y: [
          {
            formatter: (val) => this.formatCurrency(val)
          },
          {
            formatter: (val) => `${val.toFixed(1)}%`
          }
        ]
      },
      legend: {
        labels: {
          colors: '#6B7280'
        }
      },
      grid: {
        borderColor: '#E5E7EB'
      }
    };

    this.charts.salesRep = new ApexCharts(container, options);
    this.charts.salesRep.render();
  }

  setupEventListeners() {
    // Only setup event listeners once using event delegation on the container
    if (this.container.hasAttribute('data-listeners-setup')) {
      console.log('Event listeners already setup, skipping...');
      return;
    }
    
    this.container.setAttribute('data-listeners-setup', 'true');
    let isProcessing = false; // Prevent multiple simultaneous requests
    
    // Use event delegation for sales rep selector
    this.container.addEventListener('change', async (e) => {
      if (e.target.id === 'sales-rep-selector') {
        const newValue = e.target.value;
        console.log('Sales rep selector changed to:', newValue);
        
        // Prevent multiple simultaneous processing
        if (isProcessing) {
          console.log('Already processing sales rep change, ignoring...');
          return;
        }
        
        // Validate the selection
        if (newValue !== 'all' && !this.dashboard?.salesRepList?.includes(newValue)) {
          console.warn('Invalid sales rep selection:', newValue);
          // Reset to current valid selection
          e.target.value = this.selectedSalesRep || 'all';
          return;
        }
        
        isProcessing = true;
        const previousSelection = this.selectedSalesRep;
        this.selectedSalesRep = newValue;
        this.isLoading = true;
        
        try {
          this.render(); // Show loading state
          
          await this.calculateDashboard();
          
          this.isLoading = false;
          this.render();
          
          // Ensure charts are re-rendered after sales rep change
          setTimeout(() => {
            this.renderCharts();
          }, 150);
          
          console.log('Successfully switched to sales rep:', this.selectedSalesRep);
          
        } catch (error) {
          console.error('Error switching sales rep:', error);
          
          // Revert to previous selection on error
          this.selectedSalesRep = previousSelection;
          this.isLoading = false;
          
          // Update dropdown to show reverted selection
          const currentSelector = document.getElementById('sales-rep-selector');
          if (currentSelector) {
            currentSelector.value = this.selectedSalesRep || 'all';
          }
          
          this.showError('Failed to load sales rep data: ' + error.message);
          this.render();
        } finally {
          isProcessing = false;
        }
      }
    });

    // Detailed view toggles
    this.setupDetailedViewListeners();
  }

  setupDetailedViewListeners() {
    // Individual opportunity details
    document.addEventListener('click', (e) => {
      if (e.target.closest('.opportunity-detail-btn')) {
        const oppId = e.target.closest('.opportunity-detail-btn').dataset.oppId;
        this.showOpportunityDetail(oppId);
      }
      
      if (e.target.closest('.close-detail-modal')) {
        this.closeDetailModal();
      }
      
      if (e.target.closest('.sales-rep-detail-btn')) {
        const repName = e.target.closest('.sales-rep-detail-btn').dataset.repName;
        this.showSalesRepDetail(repName);
      }
    });
  }

  showOpportunityDetail(oppId) {
    const opportunity = this.opportunityComponent.opportunities.find(opp => opp.OpportunityID === oppId);
    if (!opportunity) return;

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4';
    modal.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl h-[90vh] flex flex-col">
        <div class="flex justify-between items-center p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
          <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-white">Opportunity Details</h2>
          <button class="close-detail-modal text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        <div class="flex-1 overflow-y-auto p-4 sm:p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">Basic Information</h3>
              <div class="space-y-3">
                <div><span class="font-medium">ID:</span> ${opportunity.OpportunityID}</div>
                <div><span class="font-medium">Subject:</span> ${opportunity.Subject || 'N/A'}</div>
                <div><span class="font-medium">Account:</span> ${opportunity.AccountName || 'N/A'}</div>
                <div><span class="font-medium">Amount:</span> ${this.formatCurrency(parseFloat(opportunity.Amount) || 0)}</div>
                <div><span class="font-medium">Status:</span> 
                  <span class="px-2 py-1 rounded text-sm ${this.getStatusColor(opportunity.Status)}">${opportunity.Status || 'N/A'}</span>
                </div>
                <div><span class="font-medium">Sales Rep:</span> ${opportunity.OwnerEmployeeName || 'N/A'}</div>
                <div><span class="font-medium">Stage:</span> ${opportunity.Stage || 'N/A'}</div>
                <div><span class="font-medium">Probability:</span> ${opportunity.Probability || 'N/A'}%</div>
              </div>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4">Timeline & Progress</h3>
              <div class="space-y-3">
                <div><span class="font-medium">Created:</span> ${this.formatDate(opportunity.ODataInfo?.CreatedDate)}</div>
                <div><span class="font-medium">Last Modified:</span> ${this.formatDate(opportunity.LastModified)}</div>
                <div><span class="font-medium">Close Date:</span> ${this.formatDate(opportunity.CloseDate)}</div>
                <div><span class="font-medium">Sales Cycle:</span> ${this.opportunityComponent.calculateSalesCycle(opportunity)} days</div>
                <div><span class="font-medium">Days to Close:</span> ${this.calculateDaysToClose(opportunity)} days</div>
              </div>
            </div>
          </div>
          ${opportunity.Description ? `
            <div class="mt-6">
              <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Description</h3>
              <p class="text-gray-600 dark:text-gray-400">${opportunity.Description}</p>
            </div>
          ` : ''}
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
  }

  showSalesRepDetail(repName) {
    const repData = this.dashboard.salesRepPerformance[repName];
    if (!repData) return;

    // Get individual opportunities for this rep
    const repOpportunities = this.opportunityComponent.opportunities.filter(opp => 
      opp.OwnerEmployeeName === repName &&
      this.filterOpportunity(opp)
    );

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4';
    modal.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[85vh] flex flex-col">
        <div class="flex justify-between items-center p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                      <h2 class="text-lg sm:text-xl font-semibold text-gray-800 dark:text-white">Sales Rep Analysis: ${repName}</h2>
          <button class="close-detail-modal text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        <div class="flex-1 overflow-y-auto p-3 sm:p-4 min-h-0">
          <!-- Performance Summary -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-3 mb-4">
            <div class="bg-blue-50 dark:bg-blue-900/30 p-3 rounded-lg">
              <h4 class="font-medium text-blue-600 dark:text-blue-400 text-sm">Total Opportunities</h4>
              <p class="text-xl font-bold text-blue-800 dark:text-blue-300">${repData.totalOpportunities}</p>
            </div>
            <div class="bg-green-50 dark:bg-green-900/30 p-3 rounded-lg">
              <h4 class="font-medium text-green-600 dark:text-green-400 text-sm">Total Revenue</h4>
              <p class="text-xl font-bold text-green-800 dark:text-green-300">${this.formatCurrency(repData.totalValue)}</p>
            </div>
            <div class="bg-yellow-50 dark:bg-yellow-900/30 p-3 rounded-lg">
              <h4 class="font-medium text-yellow-600 dark:text-yellow-400 text-sm">Win Rate</h4>
              <p class="text-xl font-bold text-yellow-800 dark:text-yellow-300">${this.formatNumber(repData.winRate, 1)}%</p>
            </div>
            <div class="bg-purple-50 dark:bg-purple-900/30 p-3 rounded-lg">
              <h4 class="font-medium text-purple-600 dark:text-purple-400 text-sm">Avg Deal Size</h4>
              <p class="text-xl font-bold text-purple-800 dark:text-purple-300">${this.formatCurrency(repData.avgOpportunityValue)}</p>
            </div>
          </div>

          <!-- Opportunity Status Breakdown -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
            <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-3">
              <h3 class="text-base font-medium text-gray-700 dark:text-gray-300 mb-3">Deal Status Breakdown</h3>
              <div class="space-y-3">
                <div class="flex justify-between items-center">
                  <span class="text-green-600 dark:text-green-400">Won Deals</span>
                  <span class="font-bold">${repData.won} (${this.formatCurrency(repOpportunities.filter(o => o.Status && o.Status.toLowerCase() === 'won').reduce((sum, o) => sum + (parseFloat(o.Amount) || 0), 0))})</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-red-600 dark:text-red-400">Lost Deals</span>
                  <span class="font-bold">${repData.lost} (${this.formatCurrency(repOpportunities.filter(o => o.Status && o.Status.toLowerCase() === 'lost').reduce((sum, o) => sum + (parseFloat(o.Amount) || 0), 0))})</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-blue-600 dark:text-blue-400">Open Pipeline</span>
                  <span class="font-bold">${repData.pending} (${this.formatCurrency(repOpportunities.filter(o => o.Status !== 'Won' && o.Status !== 'Lost').reduce((sum, o) => sum + (parseFloat(o.Amount) || 0), 0))})</span>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-3">
              <h3 class="text-base font-medium text-gray-700 dark:text-gray-300 mb-3">Performance Analytics</h3>
              <div class="space-y-3">
                <div class="border-l-4 border-blue-400 pl-3">
                  <div class="flex justify-between items-center">
                    <span class="font-medium">Sales Cycle Efficiency</span>
                    <span class="font-bold text-blue-600">${this.formatNumber(repData.avgSalesCycle, 0)} days</span>
                  </div>
                  <div class="text-xs text-gray-500 mt-1">
                    vs. team average: ${repData.avgSalesCycle < 90 ? 'Faster' : repData.avgSalesCycle > 120 ? 'Slower' : 'On par'} 
                    (${repData.avgSalesCycle < 90 ? '-' : '+'}${Math.abs(repData.avgSalesCycle - 105)} days)
                  </div>
                </div>
                
                <div class="border-l-4 border-green-400 pl-3">
                  <div class="flex justify-between items-center">
                    <span class="font-medium">Win Rate Performance</span>
                    <span class="font-bold text-green-600">${this.formatNumber(repData.winRate, 1)}%</span>
                  </div>
                  <div class="text-xs text-gray-500 mt-1">
                    ${repData.won} wins out of ${repData.won + repData.lost} closed deals
                  </div>
                </div>
                
                <div class="border-l-4 border-purple-400 pl-3">
                  <div class="flex justify-between items-center">
                    <span class="font-medium">Revenue per Deal</span>
                    <span class="font-bold text-purple-600">${this.formatCurrency(repData.avgOpportunityValue)}</span>
                  </div>
                  <div class="text-xs text-gray-500 mt-1">
                    Total revenue: ${this.formatCurrency(repData.totalValue)}
                  </div>
                </div>
                
                <div class="border-l-4 border-yellow-400 pl-3">
                  <div class="flex justify-between items-center">
                    <span class="font-medium">Activity Velocity</span>
                    <span class="font-bold text-yellow-600">${this.calculateActivityScore(repOpportunities)}/100</span>
                  </div>
                  <div class="text-xs text-gray-500 mt-1">
                    ${repOpportunities.filter(o => {
                      const lastMod = new Date(o.LastModified);
                      const daysSince = Math.floor((new Date() - lastMod) / (1000 * 60 * 60 * 24));
                      return daysSince <= 30;
                    }).length} active this month
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Individual Opportunities Table -->
          <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-3">
            <h3 class="text-base font-medium text-gray-700 dark:text-gray-300 mb-3">Individual Opportunities</h3>
            <div class="overflow-x-auto max-h-80 overflow-y-auto border rounded">
              <table class="w-full text-sm">
                <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0">
                  <tr>
                    <th class="px-2 sm:px-4 py-2 text-left text-xs sm:text-sm">ID</th>
                    <th class="px-2 sm:px-4 py-2 text-left text-xs sm:text-sm">Subject</th>
                    <th class="px-2 sm:px-4 py-2 text-left text-xs sm:text-sm hidden sm:table-cell">Account</th>
                    <th class="px-2 sm:px-4 py-2 text-left text-xs sm:text-sm">Amount</th>
                    <th class="px-2 sm:px-4 py-2 text-left text-xs sm:text-sm">Status</th>
                    <th class="px-2 sm:px-4 py-2 text-left text-xs sm:text-sm hidden md:table-cell">Stage</th>
                    <th class="px-2 sm:px-4 py-2 text-left text-xs sm:text-sm hidden lg:table-cell">Days</th>
                    <th class="px-2 sm:px-4 py-2 text-left text-xs sm:text-sm">Action</th>
                  </tr>
                </thead>
                <tbody>
                  ${repOpportunities.slice(0, 20).map(opp => `
                    <tr class="border-t border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td class="px-2 sm:px-4 py-2 text-xs sm:text-sm">${opp.OpportunityID}</td>
                      <td class="px-2 sm:px-4 py-2 text-xs sm:text-sm">
                        <div class="truncate max-w-xs" title="${opp.Subject || 'N/A'}">${opp.Subject || 'N/A'}</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400 sm:hidden">${opp.AccountName || 'N/A'}</div>
                      </td>
                      <td class="px-2 sm:px-4 py-2 text-xs sm:text-sm hidden sm:table-cell">
                        <div class="truncate max-w-xs" title="${opp.AccountName || 'N/A'}">${opp.AccountName || 'N/A'}</div>
                      </td>
                      <td class="px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium">${this.formatCurrency(parseFloat(opp.Amount) || 0)}</td>
                      <td class="px-2 sm:px-4 py-2">
                        <span class="px-2 py-1 rounded text-xs ${this.getStatusColor(opp.Status)}">${opp.Status || 'N/A'}</span>
                      </td>
                      <td class="px-2 sm:px-4 py-2 text-xs sm:text-sm hidden md:table-cell">${opp.Stage || 'N/A'}</td>
                      <td class="px-2 sm:px-4 py-2 text-xs sm:text-sm hidden lg:table-cell">${this.opportunityComponent.calculateSalesCycle(opp)}</td>
                      <td class="px-2 sm:px-4 py-2">
                        <button class="opportunity-detail-btn text-blue-600 hover:text-blue-800 text-xs font-medium" data-opp-id="${opp.OpportunityID}">
                          View
                        </button>
                      </td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>
            ${repOpportunities.length > 20 ? `
              <div class="mt-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                Showing first 20 of ${repOpportunities.length} opportunities
              </div>
            ` : ''}
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
  }

  filterOpportunity(opp) {
    // Apply current filters to individual opportunity
    let passes = true;

    // Date range filter
    if (this.dateRange && this.dateRange.start && this.dateRange.end) {
      const startDate = new Date(this.dateRange.start);
      startDate.setHours(0, 0, 0, 0);
      const endDate = new Date(this.dateRange.end);
      endDate.setHours(23, 59, 59, 999);

      let dateToCheck = null;
      if (opp.ODataInfo?.CreatedDate) {
        dateToCheck = new Date(opp.ODataInfo.CreatedDate);
      } else if (opp.LastModified instanceof Date) {
        dateToCheck = opp.LastModified;
      }

      if (dateToCheck && !isNaN(dateToCheck.getTime())) {
        passes = passes && (dateToCheck >= startDate && dateToCheck <= endDate);
      } else {
        passes = false;
      }
    }

    // Status filter
    if (this.filterStatus && this.filterStatus !== 'all') {
      passes = passes && (opp.Status?.toLowerCase() === this.filterStatus.toLowerCase());
    }

    return passes;
  }

  getStatusColor(status) {
    switch (status?.toLowerCase()) {
      case 'won': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'lost': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'open': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  }

  getStageColor(stage) {
    switch (stage?.toLowerCase()) {
      case 'lead': return 'border-blue-400';
      case 'opportunity-quoted': return 'border-yellow-400';
      case 'opportunity-order': return 'border-orange-400';
      case 'won': return 'border-green-400';
      case 'lost': return 'border-red-400';
      default: return 'border-gray-400';
    }
  }

  formatDate(date) {
    if (!date) return 'N/A';
    const d = new Date(date);
    if (isNaN(d.getTime())) return 'N/A';
    return d.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  }

  calculateDaysToClose(opportunity) {
    if (!opportunity.CloseDate) return 'N/A';
    const closeDate = new Date(opportunity.CloseDate);
    const today = new Date();
    const diffTime = closeDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 'Overdue';
  }

  calculateActivityScore(opportunities) {
    // Simple activity score based on recent modifications and deal volume
    let score = 0;
    const now = new Date();
    
    opportunities.forEach(opp => {
      const lastMod = new Date(opp.LastModified);
      const daysSinceUpdate = Math.floor((now - lastMod) / (1000 * 60 * 60 * 24));
      
      // Recent activity gets higher score
      if (daysSinceUpdate <= 7) score += 10;
      else if (daysSinceUpdate <= 30) score += 5;
      else if (daysSinceUpdate <= 90) score += 2;
      
      // Higher value deals contribute more
      const amount = parseFloat(opp.Amount) || 0;
      if (amount > 100000) score += 5;
      else if (amount > 50000) score += 3;
      else if (amount > 10000) score += 1;
    });
    
    return Math.min(100, score); // Cap at 100
  }

  closeDetailModal() {
    const modals = document.querySelectorAll('.fixed.inset-0');
    modals.forEach(modal => modal.remove());
  }

  renderSalesRepAnalysis(repName) {
    const repData = this.dashboard.salesRepPerformance[repName];
    if (!repData) return '<p class="text-gray-500">No data available</p>';

    const repOpportunities = this.opportunityComponent.opportunities.filter(opp => 
      opp.OwnerEmployeeName === repName && this.filterOpportunity(opp)
    );

    // Calculate additional metrics
    const avgDealSize = repData.avgOpportunityValue;
    const monthlyTarget = 100000; // Example target - can be customized
    const monthlyProgress = (repData.totalValue / monthlyTarget) * 100;
    const activePipeline = repOpportunities.filter(o => !['Won', 'Lost'].includes(o.Status));
    const pipelineValue = activePipeline.reduce((sum, o) => sum + (parseFloat(o.Amount) || 0), 0);

    return `
      <div class="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30 p-4 rounded-lg">
        <h5 class="font-medium text-blue-600 dark:text-blue-400 text-sm">Pipeline Value</h5>
        <p class="text-xl font-bold text-blue-800 dark:text-blue-300">${this.formatCurrency(pipelineValue)}</p>
        <p class="text-xs text-blue-600 dark:text-blue-400">${activePipeline.length} active deals</p>
      </div>

      <div class="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/30 p-4 rounded-lg">
        <h5 class="font-medium text-green-600 dark:text-green-400 text-sm">Monthly Progress</h5>
        <p class="text-xl font-bold text-green-800 dark:text-green-300">${this.formatNumber(monthlyProgress, 1)}%</p>
        <div class="w-full bg-green-200 dark:bg-green-700 rounded-full h-2 mt-2">
          <div class="bg-green-500 h-2 rounded-full" style="width: ${Math.min(monthlyProgress, 100)}%"></div>
        </div>
      </div>

      <div class="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/30 p-4 rounded-lg">
        <h5 class="font-medium text-purple-600 dark:text-purple-400 text-sm">Avg Deal Size</h5>
        <p class="text-xl font-bold text-purple-800 dark:text-purple-300">${this.formatCurrency(avgDealSize)}</p>
        <div class="flex items-center justify-between mt-1">
          <p class="text-xs text-purple-600 dark:text-purple-400">vs. Company Avg</p>
          <span class="text-xs font-medium ${avgDealSize > 50000 ? 'text-green-600' : avgDealSize > 25000 ? 'text-yellow-600' : 'text-blue-600'}">
            ${avgDealSize > 50000 ? '+67%' : avgDealSize > 25000 ? '+12%' : 'Baseline'}
          </span>
        </div>
      </div>

      <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/30 dark:to-yellow-800/30 p-4 rounded-lg">
        <h5 class="font-medium text-yellow-600 dark:text-yellow-400 text-sm">Velocity Score</h5>
        <p class="text-xl font-bold text-yellow-800 dark:text-yellow-300">${this.calculateActivityScore(repOpportunities)}/100</p>
        <div class="flex items-center justify-between mt-1">
          <p class="text-xs text-yellow-600 dark:text-yellow-400">Recent Activity</p>
          <span class="text-xs font-medium">
            ${repOpportunities.filter(o => {
              const lastMod = new Date(o.LastModified);
              const daysSince = Math.floor((new Date() - lastMod) / (1000 * 60 * 60 * 24));
              return daysSince <= 7;
            }).length} updates this week
          </span>
        </div>
      </div>
    `;
  }

  renderPipelineAnalysis() {
    const opportunities = this.opportunityComponent.opportunities.filter(opp => 
      this.filterOpportunity(opp) && 
      (this.selectedSalesRep === 'all' || opp.OwnerEmployeeName === this.selectedSalesRep)
    );

    // Group by stage
    const stageAnalysis = {};
    opportunities.forEach(opp => {
      const stage = opp.Stage || 'Unknown';
      if (!stageAnalysis[stage]) {
        stageAnalysis[stage] = { count: 0, value: 0 };
      }
      stageAnalysis[stage].count++;
      stageAnalysis[stage].value += parseFloat(opp.Amount) || 0;
    });

    // Calculate pipeline health
    const totalPipelineValue = Object.values(stageAnalysis).reduce((sum, stage) => sum + stage.value, 0);
    const activePipeline = opportunities.filter(o => !['Won', 'Lost'].includes(o.Status));
    const healthScore = activePipeline.length > 0 ? 
      (activePipeline.filter(o => {
        const lastMod = new Date(o.LastModified);
        const daysSince = Math.floor((new Date() - lastMod) / (1000 * 60 * 60 * 24));
        return daysSince <= 30;
      }).length / activePipeline.length) * 100 : 0;

    return `
      <div class="space-y-3">
        <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-900 rounded">
          <span class="font-medium">Total Pipeline Value</span>
          <span class="text-lg font-bold text-green-600 dark:text-green-400">${this.formatCurrency(totalPipelineValue)}</span>
        </div>
        
        <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-900 rounded">
          <span class="font-medium">Active Opportunities</span>
          <span class="text-lg font-bold text-blue-600 dark:text-blue-400">${activePipeline.length}</span>
        </div>
        
        <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-900 rounded">
          <span class="font-medium">Pipeline Health</span>
          <div class="flex items-center">
            <span class="text-lg font-bold mr-2 ${healthScore > 70 ? 'text-green-600 dark:text-green-400' : 
              healthScore > 40 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400'}">${this.formatNumber(healthScore, 0)}%</span>
            <div class="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div class="${healthScore > 70 ? 'bg-green-500' : healthScore > 40 ? 'bg-yellow-500' : 'bg-red-500'} h-2 rounded-full" style="width: ${healthScore}%"></div>
            </div>
          </div>
        </div>

        <div class="mt-4">
          <h5 class="font-medium text-gray-700 dark:text-gray-300 mb-2">Pipeline by Stage</h5>
          ${Object.entries(stageAnalysis)
            .sort(([,a], [,b]) => b.value - a.value)
            .slice(0, 5)
            .map(([stage, data]) => {
              const percentage = totalPipelineValue > 0 ? (data.value / totalPipelineValue) * 100 : 0;
              return `
                <div class="p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded border-l-4 ${this.getStageColor(stage)}">
                  <div class="flex justify-between items-center">
                    <span class="font-medium text-sm">${stage}</span>
                    <span class="text-sm font-bold">${this.formatCurrency(data.value)}</span>
                  </div>
                  <div class="flex justify-between items-center mt-1">
                    <span class="text-xs text-gray-500">${data.count} opportunities</span>
                    <span class="text-xs font-medium">${this.formatNumber(percentage, 1)}% of pipeline</span>
                  </div>
                  <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 mt-1">
                    <div class="bg-blue-500 h-1 rounded-full" style="width: ${percentage}%"></div>
                  </div>
                </div>
              `;
            }).join('')}
        </div>
      </div>
    `;
  }

  renderForecastAnalysis() {
    const opportunities = this.opportunityComponent.opportunities.filter(opp => 
      this.filterOpportunity(opp) && 
      (this.selectedSalesRep === 'all' || opp.OwnerEmployeeName === this.selectedSalesRep)
    );

    // Calculate forecast metrics
    const won = opportunities.filter(o => o.Status && o.Status.toLowerCase() === 'won');
    const lost = opportunities.filter(o => o.Status && o.Status.toLowerCase() === 'lost');
    const pipeline = opportunities.filter(o => !o.Status || !['won', 'lost'].includes(o.Status.toLowerCase()));

    const currentQuarterStart = new Date();
    currentQuarterStart.setMonth(Math.floor(currentQuarterStart.getMonth() / 3) * 3, 1);
    currentQuarterStart.setHours(0, 0, 0, 0);

    const quarterlyWon = won.filter(o => {
      const closeDate = new Date(o.CloseDate || o.LastModified);
      return closeDate >= currentQuarterStart;
    });

    const quarterlyRevenue = quarterlyWon.reduce((sum, o) => sum + (parseFloat(o.Amount) || 0), 0);
    const pipelineRevenue = pipeline.reduce((sum, o) => sum + (parseFloat(o.Amount) || 0), 0);
    const weightedPipeline = pipeline.reduce((sum, o) => sum + ((parseFloat(o.Amount) || 0) * (parseFloat(o.Probability) || 50) / 100), 0);

    // Closing this month
    const thisMonthEnd = new Date();
    thisMonthEnd.setMonth(thisMonthEnd.getMonth() + 1, 0);
    thisMonthEnd.setHours(23, 59, 59, 999);

    const closingThisMonth = pipeline.filter(o => {
      const closeDate = new Date(o.CloseDate);
      return closeDate <= thisMonthEnd && !isNaN(closeDate.getTime());
    });

    return `
      <div class="space-y-3">
        <div class="flex justify-between items-center p-3 bg-green-50 dark:bg-green-900/30 rounded">
          <span class="font-medium text-green-700 dark:text-green-300">Quarterly Revenue</span>
          <span class="text-lg font-bold text-green-600 dark:text-green-400">${this.formatCurrency(quarterlyRevenue)}</span>
        </div>
        
        <div class="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/30 rounded">
          <span class="font-medium text-blue-700 dark:text-blue-300">Pipeline Value</span>
          <span class="text-lg font-bold text-blue-600 dark:text-blue-400">${this.formatCurrency(pipelineRevenue)}</span>
        </div>
        
        <div class="flex justify-between items-center p-3 bg-purple-50 dark:bg-purple-900/30 rounded">
          <span class="font-medium text-purple-700 dark:text-purple-300">Weighted Pipeline</span>
          <span class="text-lg font-bold text-purple-600 dark:text-purple-400">${this.formatCurrency(weightedPipeline)}</span>
        </div>
        
        <div class="p-3 bg-orange-50 dark:bg-orange-900/30 rounded">
          <div class="flex justify-between items-center">
            <span class="font-medium text-orange-700 dark:text-orange-300">Closing This Month</span>
            <span class="text-lg font-bold text-orange-600 dark:text-orange-400">${closingThisMonth.length}</span>
          </div>
          <div class="flex justify-between items-center mt-1">
            <span class="text-sm text-orange-600 dark:text-orange-400">Expected Value</span>
            <span class="text-sm font-medium">${this.formatCurrency(closingThisMonth.reduce((sum, o) => sum + (parseFloat(o.Amount) || 0), 0))}</span>
          </div>
        </div>

        <div class="mt-4 space-y-3">
          <div class="p-3 bg-gray-50 dark:bg-gray-900 rounded">
            <h5 class="font-medium text-gray-700 dark:text-gray-300 mb-2">Forecast Health Analysis</h5>
            <div class="space-y-2">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600 dark:text-gray-400">Pipeline Coverage Ratio</span>
                <span class="text-sm font-medium ${pipelineRevenue > quarterlyRevenue * 2 ? 'text-green-600 dark:text-green-400' : 
                  pipelineRevenue > quarterlyRevenue ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400'}">
                  ${this.formatNumber((pipelineRevenue / Math.max(quarterlyRevenue, 1)) * 100, 1)}%
                </span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600 dark:text-gray-400">Pipeline vs Revenue</span>
                <span class="text-sm font-medium">${this.formatCurrency(pipelineRevenue)} vs ${this.formatCurrency(quarterlyRevenue)}</span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div class="${pipelineRevenue > quarterlyRevenue * 2 ? 'bg-green-500' : 
                  pipelineRevenue > quarterlyRevenue ? 'bg-yellow-500' : 'bg-red-500'} h-2 rounded-full" 
                  style="width: ${Math.min((pipelineRevenue / Math.max(quarterlyRevenue * 3, 1)) * 100, 100)}%"></div>
              </div>
            </div>
          </div>
          
          <div class="p-3 bg-blue-50 dark:bg-blue-900/30 rounded">
            <h5 class="font-medium text-blue-700 dark:text-blue-300 mb-2">Sales Velocity Metrics</h5>
            <div class="space-y-2">
              <div class="flex justify-between items-center">
                <span class="text-sm">Lead to Opportunity Rate</span>
                <span class="font-medium">${this.formatNumber((pipeline.length / Math.max(pipeline.length + won.length + lost.length, 1)) * 100, 1)}%</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm">Win Rate (Closed Deals)</span>
                <span class="font-medium">${this.formatNumber((won.length / Math.max(won.length + lost.length, 1)) * 100, 1)}%</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm">Average Sales Cycle</span>
                <span class="font-medium">${this.formatNumber(won.reduce((sum, o) => {
                  const cycle = this.opportunityComponent.calculateSalesCycle(o);
                  return sum + (cycle !== 'N/A' ? parseInt(cycle) : 0);
                }, 0) / Math.max(won.length, 1), 0)} days</span>
              </div>
            </div>
          </div>
          
          <div class="p-3 bg-green-50 dark:bg-green-900/30 rounded">
            <h5 class="font-medium text-green-700 dark:text-green-300 mb-2">Revenue Projections</h5>
            <div class="space-y-2">
              <div class="flex justify-between items-center">
                <span class="text-sm">Monthly Target Progress</span>
                <span class="font-medium">${this.formatNumber((quarterlyRevenue / 150000) * 100, 1)}%</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm">Projected Quarter-End</span>
                <span class="font-medium">${this.formatCurrency(quarterlyRevenue + (weightedPipeline * 0.4))}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm">Risk-Adjusted Pipeline</span>
                <span class="font-medium">${this.formatCurrency(weightedPipeline)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  renderRecentOpportunities() {
    const opportunities = this.opportunityComponent.opportunities.filter(opp => 
      this.filterOpportunity(opp) && 
      (this.selectedSalesRep === 'all' || opp.OwnerEmployeeName === this.selectedSalesRep)
    );

    const recentHighValue = opportunities
      .filter(o => parseFloat(o.Amount) > 50000)
      .sort((a, b) => new Date(b.LastModified) - new Date(a.LastModified))
      .slice(0, 5);

    if (recentHighValue.length === 0) {
      return '<p class="text-sm text-gray-500 dark:text-gray-400">No recent high-value opportunities</p>';
    }

    return recentHighValue.map(opp => `
      <div class="flex justify-between items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded cursor-pointer opportunity-detail-btn" data-opp-id="${opp.OpportunityID}">
        <div>
          <p class="font-medium text-sm">${opp.Subject || opp.OpportunityID}</p>
          <p class="text-xs text-gray-500 dark:text-gray-400">${opp.AccountName || 'Unknown Account'}</p>
        </div>
        <div class="text-right">
          <p class="font-bold text-sm">${this.formatCurrency(parseFloat(opp.Amount) || 0)}</p>
          <span class="px-2 py-1 rounded text-xs ${this.getStatusColor(opp.Status)}">${opp.Status || 'N/A'}</span>
        </div>
      </div>
    `).join('');
  }

  renderClosingSoonOpportunities() {
    const opportunities = this.opportunityComponent.opportunities.filter(opp => 
      this.filterOpportunity(opp) && 
      (this.selectedSalesRep === 'all' || opp.OwnerEmployeeName === this.selectedSalesRep) &&
      (!opp.Status || !['won', 'lost'].includes(opp.Status.toLowerCase()))
    );

    const closingSoon = opportunities
      .filter(o => {
        // Only include opportunities that are not won or lost
        if (o.Status && ['won', 'lost'].includes(o.Status.toLowerCase())) {
          return false;
        }
        
        const closeDate = new Date(o.CloseDate);
        if (isNaN(closeDate.getTime())) return false;
        const today = new Date();
        const diffDays = Math.ceil((closeDate - today) / (1000 * 60 * 60 * 24));
        return diffDays <= 30 && diffDays >= 0;
      })
      .sort((a, b) => new Date(a.CloseDate) - new Date(b.CloseDate))
      .slice(0, 5);

    if (closingSoon.length === 0) {
      return '<p class="text-sm text-gray-500 dark:text-gray-400">No opportunities closing soon</p>';
    }

    return closingSoon.map(opp => {
      const daysToClose = this.calculateDaysToClose(opp);
      return `
        <div class="flex justify-between items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded cursor-pointer opportunity-detail-btn" data-opp-id="${opp.OpportunityID}">
          <div>
            <p class="font-medium text-sm">${opp.Subject || opp.OpportunityID}</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">${opp.AccountName || 'Unknown Account'}</p>
          </div>
          <div class="text-right">
            <p class="font-bold text-sm">${this.formatCurrency(parseFloat(opp.Amount) || 0)}</p>
            <p class="text-xs ${daysToClose <= 7 ? 'text-red-600 dark:text-red-400' : daysToClose <= 14 ? 'text-yellow-600 dark:text-yellow-400' : 'text-blue-600 dark:text-blue-400'}">${daysToClose} days</p>
          </div>
        </div>
      `;
    }).join('');
  }

  showError(message) {
    const errorElement = document.createElement('div');
    errorElement.id = 'dashboard-error-message';
    errorElement.className = 'mt-4 p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-md';
    errorElement.innerHTML = `
      <div class="flex items-center">
        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span>${message}</span>
      </div>
      <button class="mt-2 px-2 py-1 bg-red-200 dark:bg-red-800 rounded text-sm" id="dismiss-error">Dismiss</button>
    `;
    
    if (this.container) {
      this.container.appendChild(errorElement);
      
      // Add event listener to dismiss button
      const dismissButton = document.getElementById('dismiss-error');
      if (dismissButton) {
        dismissButton.addEventListener('click', () => {
          const errorMsg = document.getElementById('dashboard-error-message');
          if (errorMsg) {
            errorMsg.remove();
          }
        });
      }
    }
  }
} 