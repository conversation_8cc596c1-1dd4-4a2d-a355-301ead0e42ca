// Opportunity component for Sales KPI Dashboard
import { connectionManager } from '../../core/connection.js';
import { NotificationSystem } from '../../core/notifications.js';

// Import the OpportunityStatistics component
import { OpportunityStatistics } from './opportunity_statistics.js';
// Import the OpportunityInsights component
import { OpportunityInsights } from './opportunity_insights.js';
// Import the OpportunityMetrics component
import { OpportunityMetrics } from './opportunity_metrics.js';
// Import the OpportunityAnalytics component
import { OpportunityAnalytics } from './opportunity_analytics.js';
// Import the OpportunityCalendar component
import { OpportunityCalendar } from './opportunity_calendar.js';
// Import the OpportunityDashboard component
import { OpportunityDashboard } from './opportunity_dashboard.js';

export class OpportunityComponent {
  constructor(container) {
    this.container = container;
    this.opportunities = [];
    this.filteredOpportunities = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'OpportunityID';
    this.sortDirection = 'desc';
    this.filterStatus = 'all';
    this.isLoading = true;
    this.dbName = 'opportunitiesDb';
    this.storeName = 'opportunities';
    this.settingsStoreName = 'appSettings';
    this.dateRange = {
      start: null,
      end: null
    };
    this.notificationSystem = new NotificationSystem();
    
    // Add tab management
    this.currentTab = 'opportunities'; // 'opportunities', 'statistics', 'metrics', 'insights', 'analytics', or 'dashboard'
    this.statisticsComponent = null;
    this.metricsComponent = null;
    this.insightsComponent = null;
    this.analyticsComponent = null;
    this.calendarComponent = null;
    this.dashboardComponent = null;
    this.contentArea = null;
    this.tableContainer = null;
  }

  async init() {
    console.log("Initializing Opportunity component");
    
    // Only use a single loading indicator
    this.isLoading = true;
    this.render();
    
    try {
      // Initialize IndexedDB
      await this.initDatabase();
      
      // Load settings if needed
      await this.loadSettings();
      
      // Load opportunity data
      await this.loadData();
    
      // Update loading state and render again
      this.isLoading = false;
      this.render();
    
      // Set up event listeners
      this.setupEventListeners();
      
      // Initialize statistics component but don't show it yet
      this.statisticsComponent = new OpportunityStatistics(this.container, this);
      
      // Initialize metrics component but don't show it yet
      this.metricsComponent = new OpportunityMetrics(this.container, this);
      
      // Initialize insights component but don't show it yet
      this.insightsComponent = new OpportunityInsights(this.container, this);
      
      // Initialize analytics component but don't show it yet
      this.analyticsComponent = new OpportunityAnalytics(this.container, this);
      
      // Initialize calendar component but don't show it yet
      this.calendarComponent = new OpportunityCalendar(this.container, this);
      
      // Initialize dashboard component but don't show it yet
      this.dashboardComponent = new OpportunityDashboard(this.container, this);
    } catch (error) {
      console.error("Error initializing opportunities:", error);
      this.isLoading = false;
      this.showError("Failed to initialize: " + error.message);
      this.render();
    }
  }

  async initDatabase() {
    return new Promise((resolve, reject) => {
      // Try to open with current version first (don't upgrade yet)
      const checkRequest = indexedDB.open(this.dbName);
      
      checkRequest.onsuccess = (event) => {
        const db = event.target.result;
        const currentVersion = db.version;
        db.close();
        
        // Now open with the correct version based on what we found
        const version = this.settingsStoreName && !db.objectStoreNames.contains(this.settingsStoreName) ? 
          Math.max(currentVersion + 1, 2) : currentVersion;
        
        const request = indexedDB.open(this.dbName, version);

        request.onerror = (event) => {
          console.error("Error opening IndexedDB:", event.target.error);
          reject(new Error("Could not open opportunities database"));
        };

        request.onsuccess = (event) => {
          console.log("Successfully opened opportunities database");
          resolve();
        };

        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          
          // Create object store for opportunities if it doesn't exist
          if (!db.objectStoreNames.contains(this.storeName)) {
            const store = db.createObjectStore(this.storeName, { keyPath: "id" });
            
            // Create indices for common search and sort operations
            store.createIndex("OpportunityID", "OpportunityID", { unique: false });
            store.createIndex("BusinessAccount", "BusinessAccount", { unique: false });
            store.createIndex("Subject", "Subject", { unique: false });
            store.createIndex("Stage", "Stage", { unique: false });
            store.createIndex("Status", "Status", { unique: false });
            store.createIndex("Estimation", "Estimation", { unique: false });
            store.createIndex("Amount", "Amount", { unique: false });
            store.createIndex("LastModified", "LastModified", { unique: false });
          }
          
          // Create object store for app settings if it doesn't exist
          if (!db.objectStoreNames.contains(this.settingsStoreName)) {
            db.createObjectStore(this.settingsStoreName, { keyPath: "id" });
          }
          
          console.log("Opportunities database schema upgraded to version", db.version);
        };
      };
      
      checkRequest.onerror = (event) => {
        console.error("Error checking database version:", event.target.error);
        reject(new Error("Could not check database version"));
      };
    });
  }

  async loadSettings() {
    try {
      // Open database without specifying version
      const request = indexedDB.open(this.dbName);
      
      const db = await new Promise((resolve, reject) => {
        request.onsuccess = (event) => resolve(event.target.result);
        request.onerror = (event) => {
          console.error("Error opening database for loading settings:", event.target.error);
          reject(new Error("Could not open database for loading settings"));
        };
      });
      
      console.log("Successfully opened database for loading settings, version:", db.version);
      
      if (!db.objectStoreNames.contains(this.settingsStoreName)) {
        console.log("Settings store not found, using defaults");
        db.close();
        return;
      }
      
      const transaction = db.transaction([this.settingsStoreName], "readonly");
      const store = transaction.objectStore(this.settingsStoreName);
      
      // Get opportunity settings
      const opportunitySettings = await new Promise((resolve) => {
        const request = store.get("opportunitySettings");
        request.onsuccess = () => resolve(request.result);
        request.onerror = (event) => {
          console.error("Error reading opportunity settings:", event.target.error);
          resolve(null);
        };
      });
      
      if (opportunitySettings) {
        console.log("Loaded opportunity settings:", opportunitySettings);
        // Apply settings here if needed
      } else {
        console.log("No saved opportunity settings, using defaults");
      }
      
      db.close();
    } catch (error) {
      console.error("Error loading settings:", error);
      // Continue with defaults if settings can't be loaded
    }
  }

  async loadData(forceRefresh = false) {
    try {
      this.isLoading = true;
      
      // Check connection status
      const connectionStatus = connectionManager.getConnectionStatus();
      
      // When forceRefresh is true, always try to fetch from Acumatica first if connected
      if (forceRefresh && connectionStatus.acumatica.isConnected) {
        console.log("Force refreshing - fetching latest opportunities from Acumatica");
        try {
          const result = await this.fetchAcumaticaOpportunities(connectionStatus.acumatica.instance);
          
          if (result.success) {
            // Parse the data and store in IndexedDB
            this.opportunities = this.parseAcumaticaOpportunities(result.data);
            
            // Fetch additional data from OData API
            await this.fetchAndMergeODataInfo(connectionStatus.acumatica.instance);
            
            await this.storeOpportunitiesInIndexedDB(this.opportunities);
            console.log(`Refreshed and stored ${this.opportunities.length} opportunities in IndexedDB`);
            
            // Continue with filtering
            this.filteredOpportunities = [...this.opportunities];
            this.calculateTotalPages();
            this.isLoading = false;
            return;
          } else {
            console.warn("Error refreshing from Acumatica:", result.error);
            // Fall through to normal loading behavior as fallback
          }
        } catch (fetchError) {
          console.error("Error refreshing opportunities from Acumatica:", fetchError);
          // Fall through to normal loading behavior as fallback
        }
      }
      
      // If not forcing refresh or Acumatica refresh failed, follow normal loading flow
      // If not connected to Acumatica, or if offline and we have data in IndexedDB
      if (!connectionStatus.acumatica.isConnected || (!forceRefresh && !navigator.onLine)) {
        // Try to get data from IndexedDB
        this.opportunities = await this.getOpportunitiesFromIndexedDB();
        
        // If no data in IndexedDB, generate sample data
        if (this.opportunities.length === 0) {
          console.log("No opportunities in IndexedDB, generating sample data");
          this.opportunities = this.generateSampleData();
        }
      } else {
        // Connected to Acumatica, fetch real data
        console.log("Fetching opportunities from Acumatica");
        try {
          const result = await this.fetchAcumaticaOpportunities(connectionStatus.acumatica.instance);
          
          if (result.success) {
            // Parse the data and store in IndexedDB
            this.opportunities = this.parseAcumaticaOpportunities(result.data);
            
            // Fetch additional data from OData API
            await this.fetchAndMergeODataInfo(connectionStatus.acumatica.instance);
            
            await this.storeOpportunitiesInIndexedDB(this.opportunities);
            console.log(`Stored ${this.opportunities.length} opportunities in IndexedDB`);
          } else {
            // If error fetching from Acumatica, try IndexedDB
            console.warn("Error fetching from Acumatica, trying IndexedDB:", result.error);
            this.opportunities = await this.getOpportunitiesFromIndexedDB();
            
            // If still no data, generate sample data
            if (this.opportunities.length === 0) {
              this.opportunities = this.generateSampleData();
            }
          }
        } catch (fetchError) {
          console.error("Error fetching opportunities from Acumatica:", fetchError);
          // Try to get data from IndexedDB as fallback
          this.opportunities = await this.getOpportunitiesFromIndexedDB();
          
          // If no data in IndexedDB, generate sample data
          if (this.opportunities.length === 0) {
            this.opportunities = this.generateSampleData();
          }
        }
      }
      
      // Apply filters
      this.filteredOpportunities = [...this.opportunities];
      this.calculateTotalPages();
      
      this.isLoading = false;
    } catch (error) {
      console.error('Error loading opportunity data:', error);
      this.opportunities = this.generateSampleData();
      this.filteredOpportunities = [...this.opportunities];
      this.calculateTotalPages();
      this.isLoading = false;
    }
  }

  async fetchAcumaticaOpportunities(instance) {
    try {
      // Build Acumatica API URL for opportunities
      const apiUrl = `${instance}/entity/Enventbridge/22.200.001/Opportunity?$expand=Address,ContactInformation,Products,TaxDetails`;
      
      console.log("Fetching opportunities with URL:", apiUrl);
      
      // Make request with cookies through the connection manager
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        credentials: 'include'  // Include cookies for authentication
      });
      
      // Check response
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please reconnect to Acumatica.');
        }
        throw new Error(`Failed to fetch opportunities: ${response.status} ${response.statusText}`);
      }
      
      // Parse response
      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error("Error fetching opportunities from Acumatica:", error);
      return { success: false, error: error.message };
    }
  }

  parseAcumaticaOpportunities(opportunitiesData) {
    try {
      // Process opportunity data from Acumatica
      return opportunitiesData.map(opp => {
        // Function to parse date string and normalize to UTC midnight
        const parseAndNormalizeDate = (dateString) => {
          if (!dateString || typeof dateString !== 'string') return null;
          
          try {
            // Extract YYYY-MM-DD part
            const datePart = dateString.substring(0, 10); // e.g., "2025-05-21"
            
            // Validate the format (basic check)
            if (!/^\d{4}-\d{2}-\d{2}$/.test(datePart)) {
                console.warn(`Invalid date format extracted: ${datePart} from ${dateString}`);
                return null;
            }

            // Create a Date object representing midnight UTC for that day
            const date = new Date(`${datePart}T00:00:00.000Z`); 
            
            if (isNaN(date.getTime())) {
              console.warn(`Invalid date created from string: ${datePart}T00:00:00.000Z`);
              return null;
            }
            return date;
          } catch(e) {
            console.error(`Error parsing date: ${dateString}`, e);
            return null;
          }
        };

        // Extract main opportunity fields
        const id = opp.id;
        const opportunityId = opp.OpportunityID?.value || '';
        const subject = opp.Subject?.value || '';
        const businessAccount = opp.BusinessAccount?.value || '';
        const contactDisplayName = opp.ContactDisplayName?.value || '';
        const amount = parseFloat(opp.Amount?.value || 0);
        const stage = opp.Stage?.value || '';
        const status = opp.Status?.value || '';
        const currencyId = opp.CurrencyID?.value || 'USD';
        const estimation = parseAndNormalizeDate(opp.Estimation?.value);
        const ownerEmployeeName = opp.OwnerEmployeeName?.value || '';
        const lastModified = parseAndNormalizeDate(opp.LastModifiedDateTime?.value);
        const reason = opp.Reason?.value || '';
        
        // Extract address if available
        const address = opp.Address ? {
          addressLine1: opp.Address.AddressLine1?.value || '',
          addressLine2: opp.Address.AddressLine2?.value || '',
          city: opp.Address.City?.value || '',
          state: opp.Address.State?.value || '',
          postalCode: opp.Address.PostalCode?.value || '',
          country: opp.Address.Country?.value || ''
        } : null;
        
        // Extract contact information if available
        const contactInfo = opp.ContactInformation ? {
          companyName: opp.ContactInformation.CompanyName?.value || '',
          firstName: opp.ContactInformation.FirstName?.value || '',
          lastName: opp.ContactInformation.LastName?.value || '',
          email: opp.ContactInformation.Email?.value || '',
          phone1: opp.ContactInformation.Phone1?.value || '',
          phone2: opp.ContactInformation.Phone2?.value || ''
        } : null;
        
        // Extract products
        const products = Array.isArray(opp.Products) 
          ? opp.Products.map(product => ({
              id: product.id,
              inventoryId: product.InventoryID?.value || '',
              description: product.TransactionDescription?.value || '',
              quantity: parseFloat(product.Qty?.value || 0),
              unitPrice: parseFloat(product.UnitPrice?.value || 0),
              amount: parseFloat(product.Amount?.value || 0),
              uom: product.UOM?.value || ''
            })) 
          : [];
        
        // Calculate total products
        const productCount = products.length;
        
        // Calculate total amount from products (for verification)
        const calculatedTotal = products.reduce((sum, product) => sum + product.amount, 0);
        
        // Parse into our standard opportunity format
        return {
          id,
          OpportunityID: opportunityId,
          Subject: subject,
          BusinessAccount: businessAccount,
          ContactDisplayName: contactDisplayName,
          Amount: amount,
          Stage: stage,
          Status: status,
          CurrencyID: currencyId,
          Estimation: estimation,
          LastModified: lastModified,
          OwnerEmployeeName: ownerEmployeeName,
          Reason: reason,
          Address: address,
          ContactInfo: contactInfo,
          Products: products,
          ProductCount: productCount,
          CalculatedTotal: calculatedTotal
        };
      });
    } catch (error) {
      console.error("Error parsing Acumatica opportunities:", error);
      return [];
    }
  }

  async getOpportunitiesFromIndexedDB() {
    return new Promise((resolve, reject) => {
      // Open without specifying version to use existing version
      const request = indexedDB.open(this.dbName);
      
      request.onerror = (event) => {
        console.error("Error opening database:", event.target.error);
        reject(new Error("Could not open opportunities database"));
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        console.log("Successfully opened database for reading, version:", db.version);
        
        // Check if the store exists
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.close();
          reject(new Error("Opportunities store not found in database"));
          return;
        }
        
        const transaction = db.transaction([this.storeName], "readonly");
        const store = transaction.objectStore(this.storeName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          const opportunities = getAllRequest.result;
          // Parse stored date strings (YYYY-MM-DD) back into Date objects (UTC midnight)
          opportunities.forEach(opp => {
            try {
               const parseStoredDate = (dateStr) => {
                 if (!dateStr || typeof dateStr !== 'string' || !/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                   return null; // Return null if invalid format or missing
                 }
                 // Construct Date object assuming the string is YYYY-MM-DD representing UTC midnight
                 const date = new Date(`${dateStr}T00:00:00.000Z`);
                 return isNaN(date.getTime()) ? null : date;
               };

               opp.Estimation = parseStoredDate(opp.Estimation);
               opp.LastModified = parseStoredDate(opp.LastModified);
               
               // Ensure amount is a number
               opp.Amount = parseFloat(opp.Amount) || 0;
            } catch (dateError) {
               console.warn(`Error parsing stored dates for Opportunity ID ${opp.OpportunityID}:`, dateError, opp);
               // Set to null on error
               opp.Estimation = null;
            }
          });
          
          console.log(`Retrieved ${opportunities.length} opportunities from IndexedDB`);
          resolve(opportunities);
        };
        
        getAllRequest.onerror = (event) => {
          console.error("Error retrieving opportunities:", event.target.error);
          reject(new Error("Failed to retrieve opportunities from database"));
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
        
        transaction.onerror = (event) => {
          console.error("Transaction error retrieving opportunities:", event.target.error);
          db.close();
        };
      };
      
      request.onupgradeneeded = (event) => {
        // This shouldn't happen when opening an existing database without specifying version
        console.warn("Unexpected database upgrade needed during read operation");
        event.target.transaction.abort();
        reject(new Error("Database schema needs upgrade, please reload the page"));
      };
    });
  }

  async storeOpportunitiesInIndexedDB(opportunities) {
    return new Promise((resolve, reject) => {
      // Open without specifying version to use existing version
      const request = indexedDB.open(this.dbName);
      
      request.onerror = (event) => {
        console.error("Error opening database for storing:", event.target.error);
        reject(new Error("Could not open opportunities database for storing"));
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        console.log("Successfully opened database for writing, version:", db.version);
        
        // Check if the store exists
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.close();
          reject(new Error("Opportunities store not found for storing"));
          return;
        }
        
        const transaction = db.transaction([this.storeName], "readwrite");
        const store = transaction.objectStore(this.storeName);
        let count = 0;
        let errorCount = 0;
        const totalOpp = opportunities.length;
        
        // Clear existing data if this is a fresh load
        store.clear().onsuccess = () => {
           console.log(`Cleared existing opportunity data. Storing ${totalOpp} new opportunities.`);
           if (totalOpp === 0) {
               resolve(); // Nothing more to do
               db.close();
               return;
           }

          // Add each opportunity, converting dates to YYYY-MM-DD strings for storage
          opportunities.forEach(originalOpp => {
             // Create a copy to avoid modifying the original object in memory
             const oppToStore = { ...originalOpp };

             // Convert Date objects (assumed UTC midnight) to YYYY-MM-DD strings
             try {
               const formatDateToUTCString = (dateObj) => {
                   if (!(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
                       return null; // Return null if invalid or not a Date
                   }
                   // Extract UTC components and format as YYYY-MM-DD
                   const year = dateObj.getUTCFullYear();
                   const month = (dateObj.getUTCMonth() + 1).toString().padStart(2, '0');
                   const day = dateObj.getUTCDate().toString().padStart(2, '0');
                   return `${year}-${month}-${day}`;
               };

               oppToStore.Estimation = formatDateToUTCString(oppToStore.Estimation);
               oppToStore.LastModified = formatDateToUTCString(oppToStore.LastModified);
               
               // Ensure amount is a number
               oppToStore.Amount = parseFloat(oppToStore.Amount) || 0;

             } catch (conversionError) {
                 console.error(`Error converting dates to string for Opportunity ${oppToStore.OpportunityID}:`, conversionError);
                 errorCount++;
                 if (count + errorCount === totalOpp) { 
                   db.close();
                   resolve(); 
                 }
             }
            
            const addRequest = store.add(oppToStore);
            
            addRequest.onsuccess = () => {
              count++;
              if (count + errorCount === totalOpp) {
                console.log(`Successfully stored ${count} opportunities.`);
                db.close();
                resolve();
              }
            };
            
            addRequest.onerror = (event) => {
              console.error("Error storing opportunity:", oppToStore.OpportunityID, event.target.error);
              errorCount++;
              if (count + errorCount === totalOpp) {
                 console.warn(`Finished storing with ${errorCount} errors.`);
                 db.close();
                 resolve();
              }
            };
          });
        };
        
        store.clear().onerror = (event) => {
          console.error("Error clearing existing opportunity data:", event.target.error);
          db.close();
          reject(new Error("Failed to clear existing opportunities"));
        };
        
        transaction.onerror = (event) => {
          console.error("Transaction error storing opportunities:", event.target.error);
          db.close();
          reject(new Error("Failed to store opportunities"));
        };
      };
      
      request.onupgradeneeded = (event) => {
        // This shouldn't happen when opening an existing database without specifying version
        console.warn("Unexpected database upgrade needed during write operation");
        event.target.transaction.abort();
        reject(new Error("Database schema needs upgrade, please reload the page"));
      };
    });
  }

  generateSampleData() {
    const stages = ['LEAD', 'OPPORTUNITY-QUOTED', 'OPPORTUNITY-ORDER', 'WON', 'LOST'];
    const statuses = ['New', 'In Progress', 'Pending', 'Won', 'Lost'];
    const currencies = ['USD', 'CAD'];
    const accounts = [
      { id: 'CENG000619', name: 'Startec Compression & Process' },
      { id: 'CENG000512', name: 'XYZ Corporation' },
      { id: 'CENG000723', name: 'Alpha Industries' },
      { id: 'CENG000842', name: 'Omega Systems' },
      { id: 'CENG000981', name: 'Gamma Technologies' }
    ];
    const contacts = [
      'Darren Colombo',
      'Jane Smith',
      'John Doe',
      'Sarah Johnson',
      'Michael Chen'
    ];
    const owners = [
      'Mackenzie Tivendale',
      'Robert Brown',
      'Emily Jones',
      'David Wilson',
      'Lisa Garcia'
    ];
    
    // Generate sample products
    const generateProducts = () => {
      const productCount = Math.floor(Math.random() * 3) + 1;
      const products = [];
      
      for (let i = 0; i < productCount; i++) {
        const unitPrice = Math.floor(Math.random() * 10000) + 1000;
        const qty = Math.floor(Math.random() * 3) + 1;
        
        products.push({
          id: `prod-${Date.now()}-${i}`,
          inventoryId: `Q-${Math.floor(Math.random() * 999)}-SCS`,
          description: `QUOTE USE ONLY - Sample Product ${i+1}`,
          quantity: qty,
          unitPrice: unitPrice,
          amount: unitPrice * qty,
          uom: 'EACH'
        });
      }
      
      return products;
    };
    
    const sampleData = [];
    const today = new Date();
    
    for (let i = 1; i <= 50; i++) {
      const account = accounts[Math.floor(Math.random() * accounts.length)];
      const stageIndex = Math.floor(Math.random() * stages.length);
      const stage = stages[stageIndex];
      const status = statuses[stageIndex];
      const currency = currencies[Math.floor(Math.random() * currencies.length)];
      
      // Generate dates within reasonable ranges
      const lastModified = new Date(today);
      lastModified.setDate(today.getDate() - Math.floor(Math.random() * 180));
      
      const estimation = new Date(lastModified);
      estimation.setDate(lastModified.getDate() + 30 + Math.floor(Math.random() * 180));
      
      // Generate products
      const products = generateProducts();
      const totalAmount = products.reduce((sum, prod) => sum + prod.amount, 0);
      
      sampleData.push({
        id: `opp-${Date.now()}-${i}`,
        OpportunityID: `00${1000 + i}`,
        Subject: `Sample Opportunity ${i}`,
        BusinessAccount: account.id,
        ContactDisplayName: contacts[Math.floor(Math.random() * contacts.length)],
        Amount: totalAmount,
        Stage: stage,
        Status: status,
        CurrencyID: currency,
        Estimation: estimation,
        LastModified: lastModified,
        OwnerEmployeeName: owners[Math.floor(Math.random() * owners.length)],
        Address: {
          addressLine1: '123 Sample Street',
          addressLine2: 'Suite 100',
          city: 'Calgary',
          state: 'AB',
          postalCode: 'T2C5R8',
          country: 'CA'
        },
        ContactInfo: {
          companyName: account.name,
          firstName: contacts[Math.floor(Math.random() * contacts.length)].split(' ')[0],
          lastName: contacts[Math.floor(Math.random() * contacts.length)].split(' ')[1],
          email: `sample${i}@example.com`,
          phone1: '****** 555-1234',
          phone2: '****** 555-5678'
        },
        Products: products,
        ProductCount: products.length,
        CalculatedTotal: totalAmount
      });
    }
    
    return sampleData;
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredOpportunities.length / this.itemsPerPage));
    
    // Adjust current page if it's out of bounds
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    // Only use the built-in loading UI in the container
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading opportunities...</p>
      </div>
    `;
  }

  renderContent() {
    // Clear container
    this.container.innerHTML = '';
    
    // Create content area
    const contentElement = document.createElement('div');
    contentElement.id = 'opportunity-content-area';
    contentElement.className = 'bg-white dark:bg-gray-800 rounded-lg shadow p-4';
    this.container.appendChild(contentElement);
    this.contentArea = contentElement;
    
    // Render the shared header and controls
    this.renderHeader();
    
    // Create table container that will be replaced when switching views
    const tableContainer = document.createElement('div');
    tableContainer.id = 'opportunity-table-container';
    contentElement.appendChild(tableContainer);
    this.tableContainer = tableContainer;
    
    // Render the appropriate content in the table area
    this.renderActiveView();
    
    // Set up event listeners for controls
    this.setupHeaderEventListeners();
  }
  
  renderHeader() {
    // Create and append the header with controls
    const headerElement = document.createElement('div');
    // Add a unique class for scoping styles
    headerElement.className = 'opportunity-component-header flex flex-col md:flex-row justify-between items-center mb-6'; 
    
    // Add CSS for expandable tab labels, scoped to the opportunity component
    const styleEl = document.createElement('style');
    styleEl.textContent = `
      /* Scope styles to the opportunity component header */
      .opportunity-component-header .tab-btn { 
        position: relative;
        overflow: hidden;
        transition: width 0.3s ease;
        width: 40px; /* Keep initial width small */
        min-width: 40px;
      }
      .opportunity-component-header .tab-btn.active {
        width: auto; /* Expand active tab */
      }
      .opportunity-component-header .tab-btn:hover {
        width: auto; /* Expand on hover */
      }
      .opportunity-component-header .tab-label {
        opacity: 0;
        max-width: 0;
        overflow: hidden;
        white-space: nowrap;
        transition: all 0.3s ease;
        margin-left: 0;
      }
      .opportunity-component-header .tab-btn.active .tab-label,
      .opportunity-component-header .tab-btn:hover .tab-label {
        opacity: 1;
        max-width: 100px; /* Adjust max-width as needed */
        margin-left: 6px;
      }
    `;
    document.head.appendChild(styleEl);
    
    headerElement.innerHTML = `
      <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4 md:mb-0">
        ${this.currentTab === 'opportunities' ? 'Opportunities' : 
          this.currentTab === 'statistics' ? 'Opportunity Statistics' : 
          this.currentTab === 'metrics' ? 'Opportunity Metrics' :
          this.currentTab === 'calendar' ? 'Opportunity Calendar' :
          this.currentTab === 'insights' ? 'Opportunity Insights' :
          this.currentTab === 'analytics' ? 'Opportunity Analytics' :
          'Opportunity Analytics'}
      </h2>
          
          <div class="flex flex-wrap items-center gap-2">
            <div class="relative">
              <input 
                type="text" 
                id="opportunity-search" 
                placeholder="Search opportunities..." 
                class="w-full sm:w-64 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
                value="${this.searchTerm || ''}"
              >
              <button id="clear-search" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${!this.searchTerm ? 'hidden' : ''}">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            
            <select id="status-filter" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm">
              <option value="all" ${this.filterStatus === 'all' ? 'selected' : ''}>All Statuses</option>
              <option value="new" ${this.filterStatus === 'new' ? 'selected' : ''}>New</option>
              <option value="in progress" ${this.filterStatus === 'in progress' ? 'selected' : ''}>In Progress</option>
              <option value="pending" ${this.filterStatus === 'pending' ? 'selected' : ''}>Pending</option>
              <option value="won" ${this.filterStatus === 'won' ? 'selected' : ''}>Won</option>
              <option value="lost" ${this.filterStatus === 'lost' ? 'selected' : ''}>Lost</option>
            </select>
            
            <div class="flex gap-2">
              <!-- Sales Report Icon Button -->
              <button id="sales-report-button" class="tab-btn p-2 ${this.currentTab === 'opportunities' ? 'bg-blue-600 text-white active' : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'} rounded-md flex items-center" title="Sales Report">
                <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <span class="tab-label text-sm font-medium">Report</span>
              </button>
              
              <!-- Sales Report Stats Icon Button -->
              <button id="sales-report-stats-button" class="tab-btn p-2 ${this.currentTab === 'statistics' ? 'bg-blue-600 text-white active' : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'} rounded-md flex items-center" title="Sales Statistics">
                <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <span class="tab-label text-sm font-medium">Statistics</span>
              </button>
              
              <!-- Opportunity Metrics Icon Button -->
              <button id="opportunity-metrics-button" class="tab-btn p-2 ${this.currentTab === 'metrics' ? 'bg-blue-600 text-white active' : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'} rounded-md flex items-center" title="Opportunity Metrics">
                <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                </svg>
                <span class="tab-label text-sm font-medium">Metrics</span>
              </button>
              
              <!-- Opportunity Calendar Icon Button -->
              <button id="opportunity-calendar-button" class="tab-btn p-2 ${this.currentTab === 'calendar' ? 'bg-blue-600 text-white active' : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'} rounded-md flex items-center" title="Opportunity Calendar">
                <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
                <span class="tab-label text-sm font-medium">Calendar</span>
              </button>
              
              <!-- Opportunity Insights Icon Button -->
              <button id="opportunity-insights-button" class="tab-btn p-2 ${this.currentTab === 'insights' ? 'bg-blue-600 text-white active' : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'} rounded-md flex items-center" title="Opportunity Insights">
                <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="tab-label text-sm font-medium">Insights</span>
              </button>
              
              <!-- Opportunity Analytics Icon Button -->
              <button id="opportunity-analytics-button" class="tab-btn p-2 ${this.currentTab === 'analytics' ? 'bg-blue-600 text-white active' : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'} rounded-md flex items-center" title="Opportunity Analytics">
                <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                </svg>
                <span class="tab-label text-sm font-medium">Analytics</span>
              </button>
              
              <!-- Opportunity Dashboard Icon Button -->
              <button id="opportunity-dashboard-button" class="tab-btn p-2 ${this.currentTab === 'dashboard' ? 'bg-blue-600 text-white active' : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'} rounded-md flex items-center" title="Sales Dashboard">
                <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                </svg>
                <span class="tab-label text-sm font-medium">Dashboard</span>
              </button>
              
              <!-- Date Range Button -->
              <button id="date-range-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Date Range">
                <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  <circle cx="12" cy="14" r="0.5" stroke="currentColor" stroke-width="2" />
                </svg>
              </button>
              
              <!-- Refresh Button -->
              <button id="refresh-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Refresh Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </button>
              
              <!-- Export Button -->
              <button id="export-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Export Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
              </button>
              
              <!-- Settings Button -->
              <button id="settings-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Settings">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </button>
            </div>
          </div>
    `;
    
    this.contentArea.appendChild(headerElement);
  }
  
  renderActiveView() {
    if (!this.tableContainer) return;
    
    // Clear the table container
    this.tableContainer.innerHTML = '';
    
    if (this.currentTab === 'opportunities') {
      this.renderOpportunitiesView();
    } else if (this.currentTab === 'statistics') {
      this.renderStatisticsView();
    } else if (this.currentTab === 'metrics') {
      this.renderMetricsView();
    } else if (this.currentTab === 'calendar') {
      this.renderCalendarView();
    } else if (this.currentTab === 'insights') {
      this.renderInsightsView();
    } else if (this.currentTab === 'analytics') {
      this.renderAnalyticsView();
    } else if (this.currentTab === 'dashboard') {
      this.renderDashboardView();
    }
  }
  
  renderOpportunitiesView() {
    // Create the opportunities table
    this.tableContainer.innerHTML = `
        <!-- Opportunity Table -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="OwnerEmployeeName">
                Owner <span class="sort-indicator"></span>
              </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="OpportunityID">
                  ID <span class="sort-indicator"></span>
                </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="CompanyName">
                Company <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Stage">
                  Stage <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Status">
                  Status <span class="sort-indicator"></span>
                </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Reason">
                Reason <span class="sort-indicator"></span>
              </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Amount">
                  Amount <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Estimation">
                  Est. Closure <span class="sort-indicator"></span>
                </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="SalesCycle">
                Cycle (Days) <span class="sort-indicator"></span>
              </th>
                <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${this.renderTableRows()}
            </tbody>
          </table>
        </div>

        <!-- Updated Pagination -->
        <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
          <div class="text-sm text-gray-500 dark:text-gray-400">
            Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredOpportunities.length)} to 
            ${Math.min(this.currentPage * this.itemsPerPage, this.filteredOpportunities.length)} of 
            ${this.filteredOpportunities.length} results
          </div>
          
          <div class="flex items-center space-x-1">
            <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-double-left"></i>
            </button>
            <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-left"></i>
            </button>
            
            <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
              ${this.currentPage} of ${this.totalPages}
            </span>
            
            <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
              <i class="fas fa-angle-right"></i>
            </button>
            <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
              <i class="fas fa-angle-double-right"></i>
            </button>
        </div>
      </div>
    `;
    
    // Set up table-specific event listeners
    this.setupTableEventListeners();
  }

  renderStatisticsView() {
    if (this.statisticsComponent) {
      // Create a wrapper div for the statistics component
      const statsContainer = document.createElement('div');
      this.tableContainer.appendChild(statsContainer);
      
      // Update the statistics component's container
      this.statisticsComponent.container = statsContainer;
      
      // Initialize with the current date range and other filters
      if (this.dateRange.start && this.dateRange.end) {
        this.statisticsComponent.dateRange = {
          start: this.dateRange.start,
          end: this.dateRange.end
        };
      }
      
      // Pass any additional filters needed
      this.statisticsComponent.filterStatus = this.filterStatus;
      
      // Tell the statistics component not to render its own header
      this.statisticsComponent.renderHeader = false;
      
      // Initialize and render the statistics
      this.statisticsComponent.init();
    } else {
      this.tableContainer.innerHTML = '<p class="text-center p-4">Statistics component not available</p>';
    }
  }

  renderMetricsView() {
    if (this.metricsComponent) {
      // Create a wrapper div for the metrics component
      const metricsContainer = document.createElement('div');
      this.tableContainer.appendChild(metricsContainer);
      
      // Update the metrics component's container
      this.metricsComponent.container = metricsContainer;
      
      // Initialize with the current date range and other filters
      if (this.dateRange.start && this.dateRange.end) {
        this.metricsComponent.dateRange = {
          start: this.dateRange.start,
          end: this.dateRange.end
        };
      }
      
      // Pass any additional filters needed
      this.metricsComponent.filterStatus = this.filterStatus;
      
      // Tell the metrics component not to render its own header
      this.metricsComponent.renderHeader = false;
      
      // Initialize and render the metrics
      this.metricsComponent.init();
    } else {
      this.tableContainer.innerHTML = '<p class="text-center p-4">Metrics component not available</p>';
    }
  }

  renderInsightsView() {
    if (!this.insightsComponent) {
      // Create insights component if it doesn't exist yet
      this.insightsComponent = new OpportunityInsights(this.tableContainer, this);
    }
    
    // Create a wrapper div for the insights component
    const insightsContainer = document.createElement('div');
    this.tableContainer.appendChild(insightsContainer);
    
    // Update the insights component's container
    this.insightsComponent.container = insightsContainer;
    
    // Initialize with the current date range and other filters
    if (this.dateRange.start && this.dateRange.end) {
      this.insightsComponent.dateRange = {
        start: this.dateRange.start,
        end: this.dateRange.end
      };
    }
    
    // Pass any additional filters needed
    this.insightsComponent.filterStatus = this.filterStatus;
    
    // Tell the insights component not to render its own header
    this.insightsComponent.renderHeader = false;
    
    // Initialize and render the insights
    this.insightsComponent.init();
  }

  renderAnalyticsView() {
    if (this.analyticsComponent) {
      // Create a wrapper div for the analytics component
      const analyticsContainer = document.createElement('div');
      this.tableContainer.appendChild(analyticsContainer);
      
      // Update the analytics component's container
      this.analyticsComponent.container = analyticsContainer;
      
      // Initialize with the current date range and other filters
      if (this.dateRange.start && this.dateRange.end) {
        this.analyticsComponent.dateRange = {
          start: this.dateRange.start,
          end: this.dateRange.end
        };
      }
      
      // Pass any additional filters needed
      this.analyticsComponent.filterStatus = this.filterStatus;
      
      // Tell the analytics component not to render its own header
      this.analyticsComponent.renderHeader = false;
      
      // Initialize and render the analytics
      this.analyticsComponent.init();
    } else {
      this.tableContainer.innerHTML = '<p class="text-center p-4">Analytics component not available</p>';
    }
  }

  renderDashboardView() {
    if (this.dashboardComponent) {
      // Create a wrapper div for the dashboard component
      const dashboardContainer = document.createElement('div');
      this.tableContainer.appendChild(dashboardContainer);
      
      // Update the dashboard component's container
      this.dashboardComponent.container = dashboardContainer;
      
      // Initialize with the current date range and other filters
      if (this.dateRange.start && this.dateRange.end) {
        this.dashboardComponent.dateRange = {
          start: this.dateRange.start,
          end: this.dateRange.end
        };
      }
      
      // Pass any additional filters needed
      this.dashboardComponent.filterStatus = this.filterStatus;
      
      // Tell the dashboard component not to render its own header
      this.dashboardComponent.renderHeader = false;
      
      // Initialize and render the dashboard
      this.dashboardComponent.init();
    } else {
      this.tableContainer.innerHTML = '<p class="text-center p-4">Dashboard component not available</p>';
    }
  }

  /**
   * Render the calendar view
   */
  renderCalendarView() {
    // Clear the container
    this.tableContainer.innerHTML = '';
    
    // Create a container with the opportunity-calendar-container class for scoping Bootstrap CSS
    const containerElement = document.createElement('div');
    containerElement.className = 'opportunity-calendar-container'; // This class will help scope Bootstrap CSS
    this.tableContainer.appendChild(containerElement);
    
    try {
      // Set up calendar options
      const calendarOptions = {
        renderHeader: false // No need for header as we have our own tabs
      };
      
      // Apply date range filter if it's set
      if (this.dateRange && this.dateRange.start && this.dateRange.end) {
        calendarOptions.dateRange = this.dateRange;
      }
      
      // Apply status filter if it's not "all"
      if (this.filterStatus !== 'all') {
        calendarOptions.filterStatus = this.filterStatus;
      }
      
      // Create the calendar component
      this.calendarComponent = new OpportunityCalendar(containerElement, this);
      
      // Apply options
      Object.assign(this.calendarComponent, calendarOptions);
      
      // Initialize the calendar with our opportunities data
      this.calendarComponent.opportunities = this.opportunities;
      this.calendarComponent.init();
    } catch (error) {
      console.error("Error rendering calendar view:", error);
      this.tableContainer.innerHTML = `
        <div class="p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-md">
          <p><strong>Error:</strong> Failed to load calendar view.</p>
          <p class="text-sm mt-2">${error.message}</p>
        </div>
      `;
    }
  }

  // Function to update button states
  updateButtonStates() {
    const mainButton = document.getElementById('sales-report-button');
    const statsButton = document.getElementById('sales-report-stats-button');
    const metricsButton = document.getElementById('opportunity-metrics-button');
    const calendarButton = document.getElementById('opportunity-calendar-button');
    const insightsButton = document.getElementById('opportunity-insights-button');
    const analyticsButton = document.getElementById('opportunity-analytics-button');
    const dashboardButton = document.getElementById('opportunity-dashboard-button');
    
    if (mainButton && statsButton && metricsButton && calendarButton && insightsButton && analyticsButton && dashboardButton) {
      // First remove active class from all buttons
      mainButton.classList.remove('bg-blue-600', 'text-white', 'active');
      mainButton.classList.add('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
      
      statsButton.classList.remove('bg-blue-600', 'text-white', 'active');
      statsButton.classList.add('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
      
      metricsButton.classList.remove('bg-blue-600', 'text-white', 'active');
      metricsButton.classList.add('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
      
      calendarButton.classList.remove('bg-blue-600', 'text-white', 'active');
      calendarButton.classList.add('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
      
      insightsButton.classList.remove('bg-blue-600', 'text-white', 'active');
      insightsButton.classList.add('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
      
      analyticsButton.classList.remove('bg-blue-600', 'text-white', 'active');
      analyticsButton.classList.add('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
      
      dashboardButton.classList.remove('bg-blue-600', 'text-white', 'active');
      dashboardButton.classList.add('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
      
      // Then add active class to the correct button
      if (this.currentTab === 'opportunities') {
        mainButton.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
        mainButton.classList.add('bg-blue-600', 'text-white', 'active');
      } else if (this.currentTab === 'statistics') {
        statsButton.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
        statsButton.classList.add('bg-blue-600', 'text-white', 'active');
      } else if (this.currentTab === 'metrics') {
        metricsButton.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
        metricsButton.classList.add('bg-blue-600', 'text-white', 'active');
      } else if (this.currentTab === 'calendar') {
        calendarButton.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
        calendarButton.classList.add('bg-blue-600', 'text-white', 'active');
      } else if (this.currentTab === 'insights') {
        insightsButton.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
        insightsButton.classList.add('bg-blue-600', 'text-white', 'active');
      } else if (this.currentTab === 'analytics') {
        analyticsButton.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
        analyticsButton.classList.add('bg-blue-600', 'text-white', 'active');
      } else if (this.currentTab === 'dashboard') {
        dashboardButton.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
        dashboardButton.classList.add('bg-blue-600', 'text-white', 'active');
      }
    }
  }

  switchTab(tabName) {
    if (this.currentTab === tabName) return; // Already on this tab
    
    this.currentTab = tabName;
    
    // Update the header title
    const headerTitle = this.contentArea.querySelector('h2');
    if (headerTitle) {
      if (this.currentTab === 'opportunities') {
        headerTitle.textContent = 'Opportunities';
      } else if (this.currentTab === 'statistics') {
        headerTitle.textContent = 'Opportunity Statistics';
      } else if (this.currentTab === 'metrics') {
        headerTitle.textContent = 'Opportunity Metrics';
      } else if (this.currentTab === 'calendar') {
        headerTitle.textContent = 'Opportunity Calendar';
      } else if (this.currentTab === 'insights') {
        headerTitle.textContent = 'Opportunity Insights';
      } else if (this.currentTab === 'analytics') {
        headerTitle.textContent = 'Opportunity Analytics';
      } else if (this.currentTab === 'dashboard') {
        headerTitle.textContent = 'Sales Dashboard';
      }
    }
    
    // Update button states
    this.updateButtonStates();
    
    // Render the active view
    this.renderActiveView();
  }

  // Split event listeners into header and table-specific handlers
  setupHeaderEventListeners() {
    // Reset event listeners by cloning buttons to prevent multiple handlers
    this.resetButtonEventListeners();
    
    // Sales Report button - Main view
    const salesReportButton = document.getElementById('sales-report-button');
    if (salesReportButton) {
      salesReportButton.addEventListener('click', () => {
        // Switch to opportunities tab
        this.switchTab('opportunities');
      });
    }
    
    // Sales Report Stats button - Statistics view
    const salesReportStatsButton = document.getElementById('sales-report-stats-button');
    if (salesReportStatsButton) {
      salesReportStatsButton.addEventListener('click', () => {
        // Switch to statistics tab
        this.switchTab('statistics');
      });
    }
    
    // Opportunity Metrics button - Metrics view
    const opportunityMetricsButton = document.getElementById('opportunity-metrics-button');
    if (opportunityMetricsButton) {
      opportunityMetricsButton.addEventListener('click', () => {
        // Switch to metrics tab
        this.switchTab('metrics');
      });
    }
    
    // Opportunity Calendar button - Calendar view
    const opportunityCalendarButton = document.getElementById('opportunity-calendar-button');
    if (opportunityCalendarButton) {
      opportunityCalendarButton.addEventListener('click', () => {
        // Switch to calendar tab
        this.switchTab('calendar');
      });
    }
    
    // Opportunity Insights button - Insights view
    const opportunityInsightsButton = document.getElementById('opportunity-insights-button');
    if (opportunityInsightsButton) {
      opportunityInsightsButton.addEventListener('click', () => {
        // Switch to insights tab
        this.switchTab('insights');
      });
    }
    
    // Opportunity Analytics button
    const opportunityAnalyticsButton = document.getElementById('opportunity-analytics-button');
    if (opportunityAnalyticsButton) {
      opportunityAnalyticsButton.addEventListener('click', () => {
        // Switch to analytics tab
        this.switchTab('analytics');
      });
    }
    
    // Opportunity Dashboard button
    const opportunityDashboardButton = document.getElementById('opportunity-dashboard-button');
    if (opportunityDashboardButton) {
      opportunityDashboardButton.addEventListener('click', () => {
        // Switch to dashboard tab
        this.switchTab('dashboard');
      });
    }
    
    // Search input
    const searchInput = document.getElementById('opportunity-search');
    if (searchInput) {
      searchInput.addEventListener('input', this.debounce(() => {
        this.searchTerm = searchInput.value.trim();
        this.currentPage = 1;
        this.applyFilters();
      }, 300));
    }

    // Clear search
    const clearSearchBtn = document.getElementById('clear-search');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        this.searchTerm = '';
        if (searchInput) searchInput.value = '';
        this.currentPage = 1;
        this.applyFilters();
      });
    }

    // Status filter
    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
      statusFilter.addEventListener('change', () => {
        this.filterStatus = statusFilter.value;
        this.currentPage = 1;
        this.applyFilters();
      });
    }
    
    // Refresh button
    const refreshButton = document.getElementById('refresh-button');
    if (refreshButton) {
      refreshButton.addEventListener('click', () => {
        this.refreshData();
      });
    }

    // Export button
    const exportButton = document.getElementById('export-button');
    if (exportButton) {
      exportButton.addEventListener('click', () => {
        this.exportOpportunityData();
      });
    }

    // Settings button
    const settingsButton = document.getElementById('settings-button');
    if (settingsButton) {
      settingsButton.addEventListener('click', () => {
        this.showSettings();
      });
    }

    // Date Range button
    const dateRangeButton = document.getElementById('date-range-button');
    if (dateRangeButton) {
      dateRangeButton.addEventListener('click', () => {
        this.showDateRangePicker();
      });
    }
  }
  
  // Helper function to reset button event listeners
  resetButtonEventListeners() {
    const buttons = [
      'sales-report-button',
      'sales-report-stats-button',
      'opportunity-metrics-button',
      'opportunity-calendar-button',
      'opportunity-insights-button',
      'opportunity-analytics-button',
      'opportunity-dashboard-button',
      'refresh-button',
      'export-button',
      'settings-button',
      'date-range-button'
    ];
    
    buttons.forEach(id => {
      const button = document.getElementById(id);
      if (button) {
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
      }
    });
  }
  
  setupTableEventListeners() {
    // Only set up these if we're on the opportunities view
    if (this.currentTab !== 'opportunities') return;

    // Sort headers
    const sortHeaders = document.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'asc';
        }
        this.applyFilters();
      });
    });

    // Pagination event handlers
    const firstPageBtn = document.getElementById('first-page');
    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage = 1;
          this.renderActiveView();
        }
      });
    }

    const prevPageBtn = document.getElementById('prev-page');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.renderActiveView();
        }
      });
    }

    const nextPageBtn = document.getElementById('next-page');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.renderActiveView();
        }
      });
    }

    const lastPageBtn = document.getElementById('last-page');
    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage = this.totalPages;
          this.renderActiveView();
        }
      });
    }

    // View opportunity buttons
    const viewButtons = document.querySelectorAll('.view-opportunity');
    viewButtons.forEach(button => {
      button.addEventListener('click', () => {
        const oppId = button.getAttribute('data-id');
        this.viewOpportunity(oppId);
      });
    });

    // Edit opportunity buttons
    const editButtons = document.querySelectorAll('.edit-opportunity');
    editButtons.forEach(button => {
      button.addEventListener('click', () => {
        const oppId = button.getAttribute('data-id');
        this.editOpportunity(oppId);
      });
    });
  }

  setupEventListeners() {
    // This method is no longer needed as we've split the event listeners
    // into setupHeaderEventListeners() and setupTableEventListeners()
  }
  
  applyFilters() {
    // Filter by search term
    let filtered = this.opportunities;
    
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(opp => 
        (opp.OpportunityID?.toLowerCase().includes(term)) ||
        (opp.Subject?.toLowerCase().includes(term)) ||
        (opp.BusinessAccount?.toLowerCase().includes(term)) ||
        (opp.ContactDisplayName?.toLowerCase().includes(term))
      );
    }
    
    // Filter by status
    if (this.filterStatus !== 'all') {
      filtered = filtered.filter(opp => 
        opp.Status?.toLowerCase() === this.filterStatus.toLowerCase()
      );
    }
    
    // Filter by date range if specified - always use CreatedDate
    if (this.dateRange.start && this.dateRange.end) {
      const startDate = new Date(this.dateRange.start);
      startDate.setHours(0, 0, 0, 0); // Ensure start of day comparison
      const endDate = new Date(this.dateRange.end);
      endDate.setHours(23, 59, 59, 999); // Ensure end of day comparison
      
      filtered = filtered.filter(opp => {
        let dateToCheck = null;
        
        // Prioritize CreatedDate if available
        if (opp.ODataInfo?.CreatedDate) {
          dateToCheck = new Date(opp.ODataInfo.CreatedDate);
        } else if (opp.LastModified instanceof Date) {
          // Fall back to LastModified if CreatedDate not available
          dateToCheck = opp.LastModified;
        }
        
        if (dateToCheck && !isNaN(dateToCheck.getTime())) {
          return dateToCheck >= startDate && dateToCheck <= endDate;
        }
        
        return false; // Filter out opportunities with invalid dates
      });
    }
    
    // Sort the data
    filtered.sort((a, b) => {
      let comparison = 0;
      const fieldA = a[this.sortField];
      const fieldB = b[this.sortField];

      // Handle null/undefined values
      const valA = fieldA ?? '';
      const valB = fieldB ?? '';

      switch (this.sortField) {
        case 'OpportunityID':
          // Parse OpportunityID as integers for proper numerical sorting
          const idA = parseInt(valA.replace(/\D/g, '')) || 0;
          const idB = parseInt(valB.replace(/\D/g, '')) || 0;
          comparison = idA - idB;
          break;
        case 'Subject':
        case 'BusinessAccount':
        case 'Stage':
        case 'Status':
        case 'OwnerEmployeeName':
        case 'Reason':
          comparison = String(valA).localeCompare(String(valB));
          break;
        case 'Amount':
          comparison = parseFloat(valA) - parseFloat(valB);
          break;
        case 'SalesCycle':
          // For missing sales cycle values, treat as infinity for sorting
          const cycleA = valA === 'N/A' ? Infinity : parseInt(valA);
          const cycleB = valB === 'N/A' ? Infinity : parseInt(valB);
          comparison = cycleA - cycleB;
          break;
        case 'Estimation':
          // Ensure valid dates before comparing
          const dateA = (valA instanceof Date && !isNaN(valA)) ? valA.getTime() : 0;
          const dateB = (valB instanceof Date && !isNaN(valB)) ? valB.getTime() : 0;
          comparison = dateA - dateB;
          break;
        default:
          comparison = String(valA).localeCompare(String(valB));
      }

      return this.sortDirection === 'asc' ? comparison : -comparison;
    });
    
    this.filteredOpportunities = filtered;
    this.calculateTotalPages();
    
    // Re-render the active view instead of the whole component
    if (this.tableContainer) {
      this.renderActiveView();
    } else {
    this.render();
    }
    
    // Update statistics component if it exists
    if (this.statisticsComponent) {
      this.statisticsComponent.filterStatus = this.filterStatus;
      this.statisticsComponent.dateRange = { ...this.dateRange };
      this.statisticsComponent.calculateStatistics().then(() => {
        if (this.currentTab === 'statistics') {
          this.statisticsComponent.render();
        }
      });
    }
    
    // Update metrics component if it exists
    if (this.metricsComponent) {
      this.metricsComponent.filterStatus = this.filterStatus;
      this.metricsComponent.dateRange = { ...this.dateRange };
      this.metricsComponent.calculateMetrics().then(() => {
        if (this.currentTab === 'metrics') {
          this.metricsComponent.render();
        }
      });
    }
    
    // Update insights component if it exists
    if (this.insightsComponent) {
      this.insightsComponent.filterStatus = this.filterStatus;
      this.insightsComponent.dateRange = { ...this.dateRange };
      this.insightsComponent.calculateInsights().then(() => {
        if (this.currentTab === 'insights') {
          this.insightsComponent.render();
        }
      });
    }
    
    // Update dashboard component if it exists
    if (this.dashboardComponent) {
      this.dashboardComponent.filterStatus = this.filterStatus;
      this.dashboardComponent.dateRange = { ...this.dateRange };
      this.dashboardComponent.calculateDashboard().then(() => {
        if (this.currentTab === 'dashboard') {
          this.dashboardComponent.render();
        }
      });
    }
  }

  // Update the showDateRangePicker method to refresh all component views
  showDateRangePicker() {
    // Create date range picker modal
    const today = new Date();
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(today.getMonth() - 1);
    
    // Format dates for input fields
    const formatDateForInput = (date) => {
      if (!date) return '';
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };
    
    const startDateValue = this.dateRange.start ? formatDateForInput(this.dateRange.start) : formatDateForInput(oneMonthAgo);
    const endDateValue = this.dateRange.end ? formatDateForInput(this.dateRange.end) : formatDateForInput(today);
    
    const modalContent = `
      <div class="p-6">
        <h3 class="text-lg font-medium mb-4">Select Date Range</h3>
        
        <div class="mb-6">
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Filter opportunities by their creation date. Only opportunities created within this date range will be displayed.
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Start Date</label>
              <input 
                type="date" 
                id="date-range-start" 
                class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"
                value="${startDateValue}"
              >
            </div>
            
            <div>
              <label class="block text-sm font-medium mb-1">End Date</label>
              <input 
                type="date" 
                id="date-range-end" 
                class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"
                value="${endDateValue}"
              >
            </div>
          </div>
        </div>
        
        <div class="flex justify-between">
          <div>
            <button id="date-range-clear" class="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md">
              Clear Filter
            </button>
          </div>
          <div class="flex gap-2">
            <button id="date-range-cancel" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
              Cancel
            </button>
            <button id="date-range-apply" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
              Apply Filter
            </button>
          </div>
        </div>
      </div>
    `;
    
    // Create modal dialog
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalOverlay.id = 'date-range-modal';
    
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full';
    modalContainer.innerHTML = modalContent;
    
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);
    
    // Setup event listeners for the modal
    const cancelButton = document.getElementById('date-range-cancel');
    const applyButton = document.getElementById('date-range-apply');
    const clearButton = document.getElementById('date-range-clear');
    const startDateInput = document.getElementById('date-range-start');
    const endDateInput = document.getElementById('date-range-end');
    
    const closeModal = () => {
      document.body.removeChild(modalOverlay);
    };
    
    if (cancelButton) {
      cancelButton.addEventListener('click', closeModal);
    }
    
    if (clearButton) {
      clearButton.addEventListener('click', () => {
        this.dateRange = {
          start: null,
          end: null,
          filterField: 'CreatedDate' // Always use CreatedDate as filter field
        };
        
        // Reset to default filtering
        this.currentPage = 1;
        this.applyFilters();
        
        // Update statistics component always
        if (this.statisticsComponent) {
          this.statisticsComponent.dateRange = { 
            start: null, 
            end: null,
            filterField: 'CreatedDate'
          };
          this.statisticsComponent.calculateStatistics().then(() => {
            if (this.currentTab === 'statistics') {
              this.statisticsComponent.render();
            }
          });
        }
        
        // Update metrics component
        if (this.metricsComponent) {
          this.metricsComponent.dateRange = { 
            start: null, 
            end: null,
            filterField: 'CreatedDate'
          };
          this.metricsComponent.calculateMetrics().then(() => {
            if (this.currentTab === 'metrics') {
              this.metricsComponent.render();
            }
          });
        }
        
        // Update insights component too
        if (this.insightsComponent) {
          this.insightsComponent.dateRange = { 
            start: null, 
            end: null,
            filterField: 'CreatedDate'
          };
          this.insightsComponent.calculateInsights().then(() => {
            if (this.currentTab === 'insights') {
              this.insightsComponent.render();
            }
          });
        }
        
        // Update dashboard component
        if (this.dashboardComponent) {
          this.dashboardComponent.dateRange = { 
            start: null, 
            end: null,
            filterField: 'CreatedDate'
          };
          this.dashboardComponent.calculateDashboard().then(() => {
            if (this.currentTab === 'dashboard') {
              this.dashboardComponent.render();
            }
          });
        }
        
        // Show confirmation and close
        this.notificationSystem.addNotification("Date filter cleared", "success");
        closeModal();
      });
    }
    
    if (applyButton) {
      applyButton.addEventListener('click', () => {
        // Get selected dates
        const startDate = startDateInput.value ? new Date(startDateInput.value) : null;
        const endDate = endDateInput.value ? new Date(endDateInput.value) : null;
        
        // Validate dates
        if (startDate && endDate && startDate > endDate) {
          this.notificationSystem.addNotification("Start date cannot be after end date", "error");
          return;
        }
        
        // Apply date filter
        this.dateRange = {
          start: startDate,
          end: endDate,
          filterField: 'CreatedDate' // Always use CreatedDate as filter field
        };
        
        // Apply filters and reset to first page
        this.currentPage = 1;
        this.applyFilters();
        
        // Update statistics component always
        if (this.statisticsComponent) {
          this.statisticsComponent.dateRange = { 
            start: startDate, 
            end: endDate,
            filterField: 'CreatedDate'
          };
          this.statisticsComponent.calculateStatistics().then(() => {
            if (this.currentTab === 'statistics') {
              this.statisticsComponent.render();
            }
          });
        }
        
        // Update metrics component
        if (this.metricsComponent) {
          this.metricsComponent.dateRange = { 
            start: startDate, 
            end: endDate,
            filterField: 'CreatedDate'
          };
          this.metricsComponent.calculateMetrics().then(() => {
            if (this.currentTab === 'metrics') {
              this.metricsComponent.render();
            }
          });
        }
        
        // Update insights component too
        if (this.insightsComponent) {
          this.insightsComponent.dateRange = { 
            start: startDate, 
            end: endDate,
            filterField: 'CreatedDate'
          };
          this.insightsComponent.calculateInsights().then(() => {
            if (this.currentTab === 'insights') {
              this.insightsComponent.render();
            }
          });
        }
        
        // Update dashboard component
        if (this.dashboardComponent) {
          this.dashboardComponent.dateRange = { 
            start: startDate, 
            end: endDate,
            filterField: 'CreatedDate'
          };
          this.dashboardComponent.calculateDashboard().then(() => {
            if (this.currentTab === 'dashboard') {
              this.dashboardComponent.render();
            }
          });
        }
        
        // Show confirmation and close
        if (startDate && endDate) {
          this.notificationSystem.addNotification(`Filtered opportunities from ${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}`, "success");
        }
        closeModal();
      });
    }
    
    // Add keyboard event for Escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        closeModal();
      }
    }, { once: true });
  }

  // Handle refresh action for all views
  refreshData() {
    // Force refresh from Acumatica
    this.loadData(true)
      .then(() => {
        this.notificationSystem.addNotification("Opportunity data refreshed successfully", "success");
        
        // Refresh statistics if statistics component exists
        if (this.statisticsComponent) {
          this.statisticsComponent.dateRange = { ...this.dateRange };
          this.statisticsComponent.filterStatus = this.filterStatus;
          this.statisticsComponent.calculateStatistics().then(() => {
            if (this.currentTab === 'statistics') {
              this.statisticsComponent.render();
            }
          });
        }
        
        // Refresh metrics if metrics component exists
        if (this.metricsComponent) {
          this.metricsComponent.dateRange = { ...this.dateRange };
          this.metricsComponent.filterStatus = this.filterStatus;
          this.metricsComponent.calculateMetrics().then(() => {
            if (this.currentTab === 'metrics') {
              this.metricsComponent.render();
            }
          });
        }
        
        // Refresh insights if insights component exists
        if (this.insightsComponent) {
          this.insightsComponent.dateRange = { ...this.dateRange };
          this.insightsComponent.filterStatus = this.filterStatus;
          this.insightsComponent.calculateInsights().then(() => {
            if (this.currentTab === 'insights') {
              this.insightsComponent.render();
            }
          });
        }
        
        // Refresh dashboard if dashboard component exists
        if (this.dashboardComponent) {
          this.dashboardComponent.dateRange = { ...this.dateRange };
          this.dashboardComponent.filterStatus = this.filterStatus;
          this.dashboardComponent.calculateDashboard().then(() => {
            if (this.currentTab === 'dashboard') {
              this.dashboardComponent.render();
            }
          });
        }
      })
      .catch(error => {
        this.notificationSystem.addNotification("Error refreshing data: " + error.message, "error");
      });
  }

  renderTableRows() {
    if (this.filteredOpportunities.length === 0) {
      return `
        <tr>
          <td colspan="10" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
            No opportunities found matching your criteria
          </td>
        </tr>
      `;
    }

    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = Math.min(start + this.itemsPerPage, this.filteredOpportunities.length);
    const displayedOpportunities = this.filteredOpportunities.slice(start, end);

    return displayedOpportunities.map(opp => `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">
          ${this.escapeHtml(opp.OwnerEmployeeName)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400 font-medium">
          ${this.escapeHtml(opp.OpportunityID)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">
          ${this.escapeHtml(opp.ContactInfo?.companyName || '')}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.escapeHtml(opp.Stage)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <span class="px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusClass(opp.Status)}">
            ${this.escapeHtml(opp.Status)}
          </span>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.escapeHtml(opp.Reason || '')}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.formatAmount(opp.Amount, opp.CurrencyID)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.formatDate(opp.Estimation)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.calculateSalesCycle(opp)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
          <button data-id="${opp.id}" class="view-opportunity text-blue-600 hover:text-blue-900 dark:hover:text-blue-400 mr-3">
            <i class="fas fa-eye"></i>
          </button>
          <button data-id="${opp.id}" class="edit-opportunity text-blue-600 hover:text-blue-900 dark:hover:text-blue-400">
            <i class="fas fa-pencil-alt"></i>
          </button>
        </td>
      </tr>
    `).join('');
  }

  formatDate(date) {
    if (!(date instanceof Date) || isNaN(date.getTime())) return 'N/A';
    
    try {
      const year = date.getUTCFullYear();
      const month = date.getUTCMonth();
      const day = date.getUTCDate();
      
      const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
      
      return `${monthNames[month]} ${day}, ${year}`;
    } catch (e) {
      console.error("Error formatting date:", e);
      return 'N/A';
    }
  }

  formatAmount(amount, currencyCode) {
    if (amount === null || amount === undefined) return 'N/A';
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode || 'USD',
      currencyDisplay: 'symbol'
    }).format(amount);
  }

  getStatusClass(status) {
    switch (status.toLowerCase()) {
      case 'new':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'in progress':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'pending':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'won':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'lost':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  }

  escapeHtml(text) {
    if (!text) return '';
    
    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  debounce(func, wait) {
    let timeout;
    return function(...args) {
      const context = this;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), wait);
    };
  }

  viewOpportunity(oppId) {
    const opp = this.opportunities.find(o => o.id === oppId);
    if (!opp) return;
    
    // Format dates for display
    const formattedEstimation = this.formatDate(opp.Estimation);
    const formattedLastModified = this.formatDate(opp.LastModified);
    
    // Format OData dates if available
    const formattedODataDates = opp.ODataInfo ? {
      DateQuotetoCustomer: opp.ODataInfo.DateQuotetoCustomer ? this.formatDate(new Date(opp.ODataInfo.DateQuotetoCustomer)) : 'N/A',
      DateOppClosed: opp.ODataInfo.DateOppClosed ? this.formatDate(new Date(opp.ODataInfo.DateOppClosed)) : 'N/A',
      RFQDate: opp.ODataInfo.RFQDate ? this.formatDate(new Date(opp.ODataInfo.RFQDate)) : 'N/A',
      CreatedDate: opp.ODataInfo.CreatedDate ? this.formatDate(new Date(opp.ODataInfo.CreatedDate)) : 'N/A',
      OrderDate: opp.ODataInfo.OrderDate ? this.formatDate(new Date(opp.ODataInfo.OrderDate)) : 'N/A'
    } : null;
    
    // Build the modal content
    const modalContent = `
      <div class="modal-content overflow-y-auto" style="max-height: 70vh;">
      <div class="p-6">
        <div class="flex flex-col md:flex-row justify-between mb-6">
          <h2 class="text-xl font-semibold mb-2">Opportunity ${this.escapeHtml(opp.OpportunityID)}</h2>
          <div class="flex items-center">
            <span class="px-2 py-1 text-sm font-semibold rounded-full ${this.getStatusClass(opp.Status)}">
              ${this.escapeHtml(opp.Status)}
            </span>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- Opportunity Details -->
          <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Opportunity Details</h3>
            <div class="space-y-2">
              <p class="text-sm flex justify-between">
                <span class="font-medium">Subject:</span> 
                <span>${this.escapeHtml(opp.Subject)}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Business Account:</span> 
                <span>${this.escapeHtml(opp.BusinessAccount)}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Contact:</span> 
                <span>${this.escapeHtml(opp.ContactDisplayName)}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Stage:</span> 
                <span>${this.escapeHtml(opp.Stage)}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Status:</span> 
                <span>${this.escapeHtml(opp.Status)}</span>
              </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Reason:</span> 
                  <span>${this.escapeHtml(opp.Reason || 'N/A')}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Sales Cycle:</span> 
                  <span>${this.calculateSalesCycle(opp)} days</span>
                </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Amount:</span> 
                <span>${this.formatAmount(opp.Amount, opp.CurrencyID)}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Estimated Closure:</span> 
                <span>${formattedEstimation}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Last Modified:</span> 
                <span>${formattedLastModified}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Owner:</span> 
                <span>${this.escapeHtml(opp.OwnerEmployeeName)}</span>
              </p>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Contact Information</h3>
            <div class="space-y-2">
              ${opp.ContactInfo ? `
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Company:</span> 
                  <span>${this.escapeHtml(opp.ContactInfo.companyName)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Name:</span> 
                  <span>${this.escapeHtml(opp.ContactInfo.firstName + ' ' + opp.ContactInfo.lastName)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Email:</span> 
                  <span>${this.escapeHtml(opp.ContactInfo.email)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Phone:</span> 
                  <span>${this.escapeHtml(opp.ContactInfo.phone1)}</span>
                </p>
                <p class="text-sm flex justify-between">
                  <span class="font-medium">Mobile:</span> 
                  <span>${this.escapeHtml(opp.ContactInfo.phone2)}</span>
                </p>
                ` : '<p class="text-sm text-gray-500 dark:text-gray-400">No contact information available</p>'}
                
                ${opp.Address ? `
                <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                  <h4 class="text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">Address</h4>
                  <p class="text-sm">${this.escapeHtml(opp.Address.addressLine1)}</p>
                  ${opp.Address.addressLine2 ? `<p class="text-sm">${this.escapeHtml(opp.Address.addressLine2)}</p>` : ''}
                  <p class="text-sm">${this.escapeHtml(opp.Address.city)}, ${this.escapeHtml(opp.Address.state)} ${this.escapeHtml(opp.Address.postalCode)}</p>
                  <p class="text-sm">${this.escapeHtml(opp.Address.country)}</p>
                </div>
                ` : ''}
            </div>
          </div>
        </div>

          ${opp.ODataInfo ? `
          <!-- AIS Monthly Sales Data -->
          <div class="mb-6">
            <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">AIS Monthly Sales Data</h3>
            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                  <p class="text-sm flex justify-between">
                    <span class="font-medium">Customer ID:</span> 
                    <span>${this.escapeHtml(opp.ODataInfo.CustomerID || 'N/A')}</span>
                  </p>
                  <p class="text-sm flex justify-between">
                    <span class="font-medium">Customer:</span> 
                    <span>${this.escapeHtml(opp.ODataInfo.Customer || 'N/A')}</span>
                  </p>
                  <p class="text-sm flex justify-between">
                    <span class="font-medium">Month:</span> 
                    <span>${this.escapeHtml(opp.ODataInfo.Month || 'N/A')}</span>
                  </p>
                  <p class="text-sm flex justify-between">
                    <span class="font-medium">Created Date:</span> 
                    <span>${formattedODataDates.CreatedDate}</span>
                  </p>
                  <p class="text-sm flex justify-between">
                    <span class="font-medium">RFQ Date:</span> 
                    <span>${formattedODataDates.RFQDate}</span>
                  </p>
                </div>
                <div class="space-y-2">
                  <p class="text-sm flex justify-between">
                    <span class="font-medium">Quote Number:</span> 
                    <span>${this.escapeHtml(opp.ODataInfo.QuoteNbr || 'N/A')}</span>
                  </p>
                  <p class="text-sm flex justify-between">
                    <span class="font-medium">Quote to Customer Date:</span> 
                    <span>${formattedODataDates.DateQuotetoCustomer}</span>
                  </p>
                  <p class="text-sm flex justify-between">
                    <span class="font-medium">Order Type:</span> 
                    <span>${this.escapeHtml(opp.ODataInfo.OrderType || 'N/A')}</span>
                  </p>
                  <p class="text-sm flex justify-between">
                    <span class="font-medium">Order Number:</span> 
                    <span>${this.escapeHtml(opp.ODataInfo.OrderNbr || 'N/A')}</span>
                  </p>
                  <p class="text-sm flex justify-between">
                    <span class="font-medium">Order Date:</span> 
                    <span>${formattedODataDates.OrderDate}</span>
                  </p>
                </div>
              </div>
              <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                <div class="space-y-2">
                  <p class="text-sm flex justify-between">
                    <span class="font-medium">Opportunity Closed Date:</span> 
                    <span>${formattedODataDates.DateOppClosed}</span>
                  </p>
                  <p class="text-sm flex justify-between">
                    <span class="font-medium">Resolution:</span> 
                    <span>${this.escapeHtml(opp.ODataInfo.Resolution || 'N/A')}</span>
                  </p>
                  <p class="text-sm flex justify-between">
                    <span class="font-medium">Sales:</span> 
                    <span>${opp.ODataInfo.Sales !== null ? this.formatAmount(opp.ODataInfo.Sales, opp.CurrencyID) : 'N/A'}</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          ` : ''}

        <!-- Products -->
        <div class="mt-6">
          <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Products (${opp.Products.length})</h3>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Item #</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
                  <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Qty</th>
                  <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Unit Price</th>
                  <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Amount</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${opp.Products.length > 0 ? opp.Products.map((product, index) => `
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${this.escapeHtml(product.inventoryId)}</td>
                    <td class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                      <div class="line-clamp-2">${this.escapeHtml(product.description)}</div>
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${product.quantity} ${product.uom}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${this.formatAmount(product.unitPrice, opp.CurrencyID)}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${this.formatAmount(product.amount, opp.CurrencyID)}</td>
                  </tr>
                `).join('') : `
                  <tr>
                    <td colspan="5" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
                      No products associated with this opportunity
                    </td>
                  </tr>
                `}
              </tbody>
              <tfoot class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th colspan="4" class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400">Total:</th>
                  <th class="px-3 py-2 text-right text-xs font-medium text-gray-800 dark:text-gray-200">${this.formatAmount(opp.Amount, opp.CurrencyID)}</th>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>

        <!-- Buttons -->
        <div class="flex justify-end gap-2 mt-6">
          <button id="opp-details-close" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
            Close
          </button>
          <button id="opp-details-edit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
            Edit
          </button>
          </div>
        </div>
      </div>
    `;
    
    // Create modal
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
    modalOverlay.id = 'opp-details-modal';
    
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-4xl h-auto max-h-[80vh]';
    modalContainer.style.cssText = 'display: flex; flex-direction: column;';
    modalContainer.innerHTML = modalContent;
    
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);
    
    // Setup event listeners
    const closeButton = document.getElementById('opp-details-close');
    const editButton = document.getElementById('opp-details-edit');
    
    const closeModal = () => {
      document.body.removeChild(modalOverlay);
    };
    
    if (closeButton) {
      closeButton.addEventListener('click', closeModal);
    }
    
    if (editButton) {
      editButton.addEventListener('click', () => {
        closeModal();
        this.editOpportunity(oppId);
      });
    }
    
    // Add keyboard event for Escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        closeModal();
      }
    }, { once: true });
  }

  editOpportunity(oppId) {
    const opp = this.opportunities.find(o => o.id === oppId);
    if (!opp) return;
    
    alert(`Editing Opportunity: ${opp.OpportunityID}`);
    // In a real app, you would implement a proper edit form
  }

  exportOpportunityData() {
    try {
      // Create CSV content
      let csvContent = 'data:text/csv;charset=utf-8,';
      csvContent += 'ID,Subject,Business Account,Stage,Status,Amount,Estimated Closure\n';
      
      // Add each row of data
      this.filteredOpportunities.forEach(opp => {
        const row = [
          opp.OpportunityID,
          opp.Subject,
          opp.BusinessAccount,
          opp.Stage,
          opp.Status,
          opp.Amount,
          this.formatDate(opp.Estimation)
        ].map(cell => `"${cell}"`).join(',');
        
        csvContent += row + '\n';
      });
      
      // Create download link
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement('a');
      link.setAttribute('href', encodedUri);
      link.setAttribute('download', `opportunities_${new Date().toISOString().slice(0, 10)}.csv`);
      document.body.appendChild(link);
      
      // Trigger download
      link.click();
      document.body.removeChild(link);
      
      this.notificationSystem.addNotification("Opportunity data exported successfully", "success");
    } catch (error) {
      console.error('Error exporting data:', error);
      this.notificationSystem.addNotification("Failed to export data: " + error.message, "error");
    }
  }

  showSettings() {
    const settingsHtml = `
      <div class="p-6">
        <h3 class="text-lg font-medium mb-4">Opportunity Settings</h3>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Items per page</label>
          <select id="settings-items-per-page" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
            <option value="5" ${this.itemsPerPage === 5 ? 'selected' : ''}>5</option>
            <option value="10" ${this.itemsPerPage === 10 ? 'selected' : ''}>10</option>
            <option value="25" ${this.itemsPerPage === 25 ? 'selected' : ''}>25</option>
            <option value="50" ${this.itemsPerPage === 50 ? 'selected' : ''}>50</option>
          </select>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Default sort</label>
          <select id="settings-default-sort" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
            <option value="OpportunityID" ${this.sortField === 'OpportunityID' ? 'selected' : ''}>ID</option>
            <option value="Subject" ${this.sortField === 'Subject' ? 'selected' : ''}>Subject</option>
            <option value="BusinessAccount" ${this.sortField === 'BusinessAccount' ? 'selected' : ''}>Business Account</option>
            <option value="Stage" ${this.sortField === 'Stage' ? 'selected' : ''}>Stage</option>
            <option value="Status" ${this.sortField === 'Status' ? 'selected' : ''}>Status</option>
            <option value="Amount" ${this.sortField === 'Amount' ? 'selected' : ''}>Amount</option>
            <option value="Estimation" ${this.sortField === 'Estimation' ? 'selected' : ''}>Est. Closure</option>
          </select>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Sort direction</label>
          <div class="flex gap-4">
            <label class="inline-flex items-center">
              <input type="radio" name="settings-sort-direction" value="asc" ${this.sortDirection === 'asc' ? 'checked' : ''} class="mr-2">
              Ascending
            </label>
            <label class="inline-flex items-center">
              <input type="radio" name="settings-sort-direction" value="desc" ${this.sortDirection === 'desc' ? 'checked' : ''} class="mr-2">
              Descending
            </label>
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-6">
          <button id="settings-cancel" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
            Cancel
          </button>
          <button id="settings-save" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
            Save Changes
          </button>
        </div>
      </div>
    `;
    
    // Create modal dialog
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalOverlay.id = 'settings-modal';
    
    const modalContent = document.createElement('div');
    modalContent.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full';
    modalContent.innerHTML = settingsHtml;
    
    modalOverlay.appendChild(modalContent);
    document.body.appendChild(modalOverlay);
    
    // Setup event listeners for the modal
    const cancelButton = document.getElementById('settings-cancel');
    const saveButton = document.getElementById('settings-save');
    
    const closeModal = () => {
      document.body.removeChild(modalOverlay);
    };
    
    if (cancelButton) {
      cancelButton.addEventListener('click', closeModal);
    }
    
    if (saveButton) {
      saveButton.addEventListener('click', async () => {
        // Get settings values
        const itemsPerPageSelect = document.getElementById('settings-items-per-page');
        const defaultSortSelect = document.getElementById('settings-default-sort');
        const sortDirectionRadios = document.getElementsByName('settings-sort-direction');
        
        if (itemsPerPageSelect) {
          this.itemsPerPage = parseInt(itemsPerPageSelect.value);
        }
        
        if (defaultSortSelect) {
          this.sortField = defaultSortSelect.value;
        }
        
        let selectedDirection = 'desc';
        sortDirectionRadios.forEach(radio => {
          if (radio.checked) {
            selectedDirection = radio.value;
          }
        });
        this.sortDirection = selectedDirection;
        
        // Save settings to IndexedDB
        await this.saveSettings();
        
        // Apply settings and re-render
        this.calculateTotalPages();
        this.applyFilters();
        
        // Close modal
        closeModal();
        
        // Show success message
        this.notificationSystem.addNotification("Settings updated successfully", "success");
      });
    }
  }

  async saveSettings() {
    try {
      // Open database without specifying version
      const request = indexedDB.open(this.dbName);
      
      const db = await new Promise((resolve, reject) => {
        request.onsuccess = (event) => resolve(event.target.result);
        request.onerror = (event) => {
          console.error("Error opening database for settings:", event.target.error);
          reject(new Error("Could not open database for saving settings"));
        };
      });
      
      if (!db.objectStoreNames.contains(this.settingsStoreName)) {
        console.error("Settings store not found, cannot save settings");
        db.close();
        return;
      }
      
      const transaction = db.transaction([this.settingsStoreName], "readwrite");
      const store = transaction.objectStore(this.settingsStoreName);
      
      // Save opportunity settings
      store.put({
        id: "opportunitySettings",
        value: {
          itemsPerPage: this.itemsPerPage,
          sortField: this.sortField,
          sortDirection: this.sortDirection
        }
      });
      
      await new Promise((resolve, reject) => {
        transaction.oncomplete = () => {
          resolve();
        };
        transaction.onerror = (event) => {
          reject(event.target.error);
        };
      });
      
      db.close();
    } catch (error) {
      console.error("Error saving settings:", error);
      this.notificationSystem.addNotification("Error saving settings: " + error.message, "error");
    }
  }

  showSalesReport() {
    alert('Sales Report view would be shown here');
    // In a real app, you would implement a proper report view
  }

  showSalesReportStats() {
    alert('Sales Report Statistics would be shown here');
    // In a real app, you would implement a proper statistics view
  }

  showOpportunityAnalytics() {
    alert('Opportunity Analytics would be shown here');
    // In a real app, you would implement a proper analytics view
  }

  showError(message) {
    const errorElement = document.createElement('div');
    errorElement.id = 'opportunity-error-message';
    errorElement.className = 'mt-4 p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-md';
    errorElement.innerHTML = `
      <div class="flex items-center">
        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span>${message}</span>
      </div>
      <button class="mt-2 px-2 py-1 bg-red-200 dark:bg-red-800 rounded text-sm" id="dismiss-error">Dismiss</button>
    `;
    
    if (this.container) {
      this.container.appendChild(errorElement);
      
      // Add event listener to dismiss button
      const dismissButton = document.getElementById('dismiss-error');
      if (dismissButton) {
        dismissButton.addEventListener('click', () => {
          const errorMsg = document.getElementById('opportunity-error-message');
          if (errorMsg) {
            errorMsg.remove();
          }
        });
      }
    } else {
      console.error("Container not available to show error:", message);
      alert("Error: " + message);
    }
  }

  async fetchAndMergeODataInfo(instance) {
    try {
      // Get credentials from connection manager for Basic Auth
      const credentials = await this.getAcumaticaCredentials();
      if (!credentials || !credentials.username || !credentials.password) {
        console.warn("Missing credentials for OData API request");
        return;
      }
      
      // Construct the OData API URL
      const oDataUrl = `${instance}/odata/Envent%20CA%20-%20Live/AIS%20Monthly%20Sales`;
      console.log("Fetching additional opportunity data with URL:", oDataUrl);
      
      // Create Basic Auth header value
      const basicAuth = 'Basic ' + btoa(`${credentials.username}:${credentials.password}`);
      
      // Make the OData API request with Basic Auth
      const response = await fetch(oDataUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': basicAuth
        }
      });
      
      // Check response
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed for OData API. Check your credentials.');
        }
        throw new Error(`Failed to fetch OData: ${response.status} ${response.statusText}`);
      }
      
      // Parse response
      const oDataResponse = await response.json();
      if (!oDataResponse.value || !Array.isArray(oDataResponse.value)) {
        throw new Error('Invalid OData response format');
      }
      
      console.log(`Received ${oDataResponse.value.length} records from OData API`);
      
      // Create a map of the OData response by OpportunityID for easier lookup
      const oDataByOpportunityId = {};
      oDataResponse.value.forEach(item => {
        const opportunityId = item.OpportunityID;
        if (opportunityId) {
          oDataByOpportunityId[opportunityId] = item;
        }
      });
      
      // Merge the OData information with our opportunities
      this.opportunities.forEach(opp => {
        const oDataInfo = oDataByOpportunityId[opp.OpportunityID];
        if (oDataInfo) {
          opp.ODataInfo = {
            Sales: oDataInfo.Sales,
            CustomerID: oDataInfo.CustomerID,
            Customer: oDataInfo.Customer,
            RFQDate: oDataInfo.RFQDate,
            Resolution: oDataInfo.Resolution,
            DateOppClosed: oDataInfo.DateOppClosed,
            DateQuotetoCustomer: oDataInfo.DateQuotetoCustomer,
            OrderDate: oDataInfo.OrderDate,
            Month: oDataInfo.Month,
            CreatedDate: oDataInfo.CreatedDate,
            QuoteNbr: oDataInfo.QuoteNbr,
            OrderType: oDataInfo.OrderType,
            OrderNbr: oDataInfo.OrderNbr,
            ContactID: oDataInfo.ContactID,
            RelationID: oDataInfo.RelationID
          };
        }
      });
      
      console.log("OData information merged with opportunities");
      return true;
    } catch (error) {
      console.error("Error fetching or merging OData information:", error);
      return false;
    }
  }
  
  async getAcumaticaCredentials() {
    try {
      // Get connection data from Chrome storage
      if (chrome?.storage?.local) {
        const data = await new Promise(resolve => {
          chrome.storage.local.get(['connections'], data => {
            resolve(data?.connections || null);
          });
        });
        
        if (data && data.acumatica && data.acumatica.credentials) {
          return data.acumatica.credentials;
        }
      }
      
      // Fallback: Try to get from connectionManager directly
      return connectionManager.connections.acumatica.credentials;
    } catch (error) {
      console.error("Error retrieving Acumatica credentials:", error);
      return null;
    }
  }

  calculateSalesCycle(opp) {
    try {
      // Check if OData info is available
      if (!opp.ODataInfo) {
        return 'N/A';
      }
      
      // Get created date and closed date from OData
      const createdDateStr = opp.ODataInfo.CreatedDate;
      const closedDateStr = opp.ODataInfo.DateOppClosed;
      
      // If either date is missing, return N/A
      if (!createdDateStr || !closedDateStr) {
        return 'N/A';
      }
      
      // Parse dates
      const createdDate = new Date(createdDateStr);
      const closedDate = new Date(closedDateStr);
      
      // Check if dates are valid
      if (isNaN(createdDate.getTime()) || isNaN(closedDate.getTime())) {
        return 'N/A';
      }
      
      // Calculate difference in days
      const diffTime = Math.abs(closedDate - createdDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      // Save the cycle value to the opportunity object for sorting
      opp.SalesCycle = diffDays;
      
      return diffDays.toString();
    } catch (error) {
      console.error("Error calculating sales cycle:", error);
      return 'N/A';
    }
  }
} 