// Insights component for Sales KPI Dashboard
import { connectionManager } from '../../core/connection.js';
import { NotificationSystem } from '../../core/notifications.js';

export class OpportunityInsights {
  constructor(container, opportunityComponent) {
    this.container = container;
    this.opportunityComponent = opportunityComponent;
    this.insights = null;
    this.isLoading = true;
    this.notificationSystem = new NotificationSystem();
    this.timeframe = 'year'; // default timeframe: year, quarter, month
    this.renderHeader = false; // Default to false - header handled by parent component
    this.dateRange = null; // Date range for filtering
    this.filterStatus = 'all'; // Status filter
    this.aiPromptOpen = false;
    this.isProcessingQuery = false;
  }

  async init() {
    console.log("Initializing Opportunity Insights component");
    this.isLoading = true;
    this.render();
    
    try {
      // Calculate insights from opportunity data
      await this.calculateInsights();
      
      this.isLoading = false;
      this.render();
      
      // Only set up event listeners if we're rendering our own header
      if (this.renderHeader) {
        this.setupEventListeners();
      }
    } catch (error) {
      console.error("Error initializing opportunity insights:", error);
      this.isLoading = false;
      this.showError("Failed to initialize insights: " + error.message);
      this.render();
    }
  }

  async calculateInsights() {
    // Get opportunities from the main component
    const opportunities = this.opportunityComponent.opportunities;

    if (!opportunities || opportunities.length === 0) {
      throw new Error("No opportunity data available");
    }

    // Get filtered opportunities based on filters
    let filteredOpportunities = [...opportunities];

    // Apply date range filter if specified
    if (this.dateRange && this.dateRange.start && this.dateRange.end) {
      const startDate = new Date(this.dateRange.start);
      startDate.setHours(0, 0, 0, 0);
      const endDate = new Date(this.dateRange.end);
      endDate.setHours(23, 59, 59, 999);

      filteredOpportunities = filteredOpportunities.filter(opp => {
        let dateToCheck = null;
        // Always prioritize CreatedDate for consistency with main component
        if (opp.ODataInfo?.CreatedDate) {
          dateToCheck = new Date(opp.ODataInfo.CreatedDate);
        } else if (opp.LastModified instanceof Date) {
          // Fall back to LastModified if CreatedDate not available
          dateToCheck = opp.LastModified;
        }

        if (dateToCheck && !isNaN(dateToCheck.getTime())) {
          return dateToCheck >= startDate && dateToCheck <= endDate;
        }
        return false;
      });
    }

    // Apply status filter if specified and not 'all'
    if (this.filterStatus && this.filterStatus !== 'all') {
      filteredOpportunities = filteredOpportunities.filter(opp => 
        opp.Status?.toLowerCase() === this.filterStatus.toLowerCase()
      );
    }

    // Initialize insights structure
    this.insights = {
      summary: {
        totalOpportunities: filteredOpportunities.length,
        totalValue: 0,
        averageOpportunityValue: 0,
        topStage: '',
        topStatus: '',
        topOwner: ''
      },
      conversionRates: {
        stageConversion: {},
        overallConversion: 0
      },
      opportunityTimeline: {
        averageTimeInStage: {},
        averageSalesCycle: 0,
        fastestClosed: null,
        slowestClosed: null
      },
      predictiveInsights: {
        winProbability: {},
        valueDistribution: {}
      }
    };

    // Calculate high-level insights
    if (filteredOpportunities.length > 0) {
      // Calculate total value
      this.insights.summary.totalValue = filteredOpportunities.reduce((sum, opp) => 
        sum + (parseFloat(opp.Amount) || 0), 0);
      
      // Calculate average opportunity value
      this.insights.summary.averageOpportunityValue = 
        this.insights.summary.totalValue / filteredOpportunities.length;
        
      // Find top stage, status, and owner by count
      const stageCounts = {};
      const statusCounts = {};
      const ownerCounts = {};
      
      filteredOpportunities.forEach(opp => {
        // Count by stage
        if (opp.Stage) {
          stageCounts[opp.Stage] = (stageCounts[opp.Stage] || 0) + 1;
        }
        
        // Count by status
        if (opp.Status) {
          statusCounts[opp.Status] = (statusCounts[opp.Status] || 0) + 1;
        }
        
        // Count by owner
        if (opp.OwnerEmployeeName) {
          ownerCounts[opp.OwnerEmployeeName] = (ownerCounts[opp.OwnerEmployeeName] || 0) + 1;
        }
      });
      
      // Find the top stage, status, and owner
      let topStageCount = 0;
      let topStatusCount = 0;
      let topOwnerCount = 0;
      
      Object.entries(stageCounts).forEach(([stage, count]) => {
        if (count > topStageCount) {
          this.insights.summary.topStage = stage;
          topStageCount = count;
        }
      });
      
      Object.entries(statusCounts).forEach(([status, count]) => {
        if (count > topStatusCount) {
          this.insights.summary.topStatus = status;
          topStatusCount = count;
        }
      });
      
      Object.entries(ownerCounts).forEach(([owner, count]) => {
        if (count > topOwnerCount) {
          this.insights.summary.topOwner = owner;
          topOwnerCount = count;
        }
      });
      
      // Calculate conversion rates between stages
      // This is a simplified version - in real world we would track individual opportunities
      const stages = Object.keys(stageCounts);
      if (stages.length > 1) {
        for (let i = 0; i < stages.length - 1; i++) {
          const currentStage = stages[i];
          const nextStage = stages[i + 1];
          
          if (stageCounts[currentStage] > 0) {
            this.insights.conversionRates.stageConversion[`${currentStage} to ${nextStage}`] = 
              ((stageCounts[nextStage] / stageCounts[currentStage]) * 100).toFixed(1);
          }
        }
      }
      
      // Calculate overall conversion (simplified)
      const closedOpps = filteredOpportunities.filter(opp => 
        opp.Status && ['won', 'lost'].includes(opp.Status.toLowerCase()));
      const wonOpps = filteredOpportunities.filter(opp => 
        opp.Status && opp.Status.toLowerCase() === 'won');
        
      if (closedOpps.length > 0) {
        this.insights.conversionRates.overallConversion = 
          ((wonOpps.length / closedOpps.length) * 100).toFixed(1);
      }
      
      // Calculate sales cycle information
      const cycleData = filteredOpportunities
        .filter(opp => this.opportunityComponent.calculateSalesCycle(opp) !== 'N/A')
        .map(opp => ({
          id: opp.OpportunityID,
          days: parseInt(this.opportunityComponent.calculateSalesCycle(opp)),
          amount: parseFloat(opp.Amount) || 0
        }));
        
      if (cycleData.length > 0) {
        // Calculate average sales cycle
        const totalDays = cycleData.reduce((sum, item) => sum + item.days, 0);
        this.insights.opportunityTimeline.averageSalesCycle = 
          (totalDays / cycleData.length).toFixed(1);
          
        // Find fastest and slowest closed opportunities
        cycleData.sort((a, b) => a.days - b.days);
        this.insights.opportunityTimeline.fastestClosed = cycleData[0];
        this.insights.opportunityTimeline.slowestClosed = cycleData[cycleData.length - 1];
      }
    }

    console.log("Insights calculated:", this.insights);
    return this.insights;
  }

  formatCurrency(value) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(value);
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderInsights();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Analyzing opportunities...</p>
      </div>
    `;
  }

  renderInsights() {
    if (!this.insights) {
      this.container.innerHTML = `
        <p class="text-center text-gray-500 dark:text-gray-400">No insights available</p>
      `;
      return;
    }

    const { summary, conversionRates, opportunityTimeline, predictiveInsights } = this.insights;

    // Create header content - only included if renderHeader is true
    let headerContent = '';
    if (this.renderHeader) {
      headerContent = `
        <!-- Timeframe selector -->
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Opportunity Insights</h2>
          <div class="inline-flex rounded-md shadow-sm" role="group">
            <button type="button" id="timeframe-year" class="px-4 py-2 text-sm font-medium ${this.timeframe === 'year' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'} rounded-l-lg">
              Year
            </button>
            <button type="button" id="timeframe-quarter" class="px-4 py-2 text-sm font-medium ${this.timeframe === 'quarter' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'} border-l border-gray-200 dark:border-gray-600">
              Quarter
            </button>
            <button type="button" id="timeframe-month" class="px-4 py-2 text-sm font-medium ${this.timeframe === 'month' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'} rounded-r-lg border-l border-gray-200 dark:border-gray-600">
              Month
            </button>
          </div>
        </div>
      `;
    }

    // Add global AI assistant button at the top
    const topAIButtonContent = `
      <div class="flex justify-end mb-4">
        <button id="askEnventAI" class="group flex items-center bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 px-3 py-1.5 rounded-full shadow-md transition-all duration-300 transform hover:scale-105">
          <svg class="w-4 h-4 text-white mr-1.5 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
          <span class="text-xs font-medium text-white">Ask Envent Bridge AI</span>
        </button>
      </div>
    `;

    this.container.innerHTML = `
      ${headerContent}
      ${topAIButtonContent}

      <!-- Key Performance Details -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Opportunity Performance -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
          <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300 flex items-center">
            <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            Opportunity Performance
          </h3>
          
          <!-- Top metrics cards -->
          <div class="grid grid-cols-3 gap-3 mb-4">
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-3 transform transition-all duration-300 hover:scale-105 hover:shadow-md">
              <div class="flex items-center mb-2">
                <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center mr-2">
                  <svg class="w-4 h-4 text-blue-600 dark:text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                  </svg>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">Top Stage</span>
              </div>
              <div class="text-sm font-medium text-gray-800 dark:text-white truncate" title="${summary.topStage || 'N/A'}">
                ${summary.topStage || 'N/A'}
              </div>
              <div class="mt-1 text-xs text-blue-600 dark:text-blue-400">
                Focus area for conversion optimization
              </div>
            </div>
            
            <div class="bg-gradient-to-br from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20 rounded-lg p-3 transform transition-all duration-300 hover:scale-105 hover:shadow-md">
              <div class="flex items-center mb-2">
                <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-800 flex items-center justify-center mr-2">
                  <svg class="w-4 h-4 text-green-600 dark:text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">Top Status</span>
              </div>
              <div class="text-sm font-medium text-gray-800 dark:text-white truncate" title="${summary.topStatus || 'N/A'}">
                ${summary.topStatus || 'N/A'}
              </div>
              <div class="mt-1 text-xs text-green-600 dark:text-green-400">
                Most common outcome in pipeline
              </div>
            </div>
            
            <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-3 transform transition-all duration-300 hover:scale-105 hover:shadow-md">
              <div class="flex items-center mb-2">
                <div class="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-800 flex items-center justify-center mr-2">
                  <svg class="w-4 h-4 text-purple-600 dark:text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
                <span class="text-xs text-gray-500 dark:text-gray-400">Top Sales Rep</span>
              </div>
              <div class="text-sm font-medium text-gray-800 dark:text-white truncate" title="${summary.topOwner || 'N/A'}">
                ${summary.topOwner || 'N/A'}
              </div>
              <div class="mt-1 text-xs text-purple-600 dark:text-purple-400">
                Highest performing team member
              </div>
            </div>
          </div>
          
          <div class="pt-1 border-t border-gray-100 dark:border-gray-700">
            <div class="flex justify-between items-center mb-3">
              <p class="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                <svg class="w-4 h-4 mr-1.5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                </svg>
                Stage Conversion Rates
              </p>
              <div class="text-xs text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                Pipeline flow health
              </div>
            </div>
            
            ${Object.keys(conversionRates.stageConversion).length > 0 ? 
              Object.entries(conversionRates.stageConversion).map(([stages, rate]) => {
                // Cap the displayed bar width at 100%
                const barWidth = Math.min(parseFloat(rate), 100);
                const barColor = barWidth > 70 ? 'bg-green-500' : barWidth > 40 ? 'bg-blue-500' : 'bg-orange-500';
                
                return `
                <div class="mb-2.5 group">
                  <div class="flex justify-between items-center mb-1">
                    <div class="flex items-center text-xs">
                      <span class="text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">${stages}</span>
                      <svg class="w-3.5 h-3.5 ml-1 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                      </svg>
                    </div>
                    <div class="flex items-center">
                      <span class="text-xs font-medium ${barWidth > 70 ? 'text-green-600 dark:text-green-400' : barWidth > 40 ? 'text-blue-600 dark:text-blue-400' : 'text-orange-600 dark:text-orange-400'} group-hover:scale-110 transition-transform">
                        ${rate}%
                      </span>
                    </div>
                  </div>
                  <div class="relative w-full">
                    <div class="w-full h-1.5 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden">
                      <div class="${barColor} h-1.5 rounded-full transition-all duration-500 ease-out group-hover:shadow-lg" 
                           style="width: ${barWidth}%; transform-origin: left center; transform: scaleX(0.98); opacity: 0.9;"
                           onmouseover="this.style.transform='scaleX(1)'; this.style.opacity='1';"
                           onmouseout="this.style.transform='scaleX(0.98)'; this.style.opacity='0.9';"></div>
                    </div>
                    <div class="absolute -right-1 top-0 bottom-0 flex items-center">
                      <div class="w-1 h-1 rounded-full ${barColor} opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    </div>
                  </div>
                </div>
                `;
              }).join('') : 
              `<div class="flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <svg class="w-6 h-6 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p class="text-sm text-gray-500 dark:text-gray-400">No stage conversion data available</p>
              </div>`
            }
          </div>
        </div>
        
        <!-- Sales Cycle Insights -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
          <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300 flex items-center">
            <svg class="w-5 h-5 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Sales Cycle Insights
          </h3>
          <div class="space-y-3">
            <div class="flex flex-col sm:flex-row sm:justify-between space-y-3 sm:space-y-0">
              <div class="bg-gray-50 dark:bg-gray-900 rounded p-3 flex-1 sm:mr-3">
                <p class="text-xs text-gray-500 dark:text-gray-400">Fastest Closed Deal</p>
                ${opportunityTimeline.fastestClosed ? `
                <p class="text-lg font-bold text-gray-800 dark:text-gray-200">${opportunityTimeline.fastestClosed.days} days</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">ID: ${opportunityTimeline.fastestClosed.id}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">Amount: ${this.formatCurrency(opportunityTimeline.fastestClosed.amount)}</p>
                ` : `<p class="text-sm text-gray-500 dark:text-gray-400">No data</p>`}
              </div>
              
              <div class="bg-gray-50 dark:bg-gray-900 rounded p-3 flex-1">
                <p class="text-xs text-gray-500 dark:text-gray-400">Slowest Closed Deal</p>
                ${opportunityTimeline.slowestClosed ? `
                <p class="text-lg font-bold text-gray-800 dark:text-gray-200">${opportunityTimeline.slowestClosed.days} days</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">ID: ${opportunityTimeline.slowestClosed.id}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">Amount: ${this.formatCurrency(opportunityTimeline.slowestClosed.amount)}</p>
                ` : `<p class="text-sm text-gray-500 dark:text-gray-400">No data</p>`}
              </div>
            </div>
            
            <div class="pt-3 border-t border-gray-100 dark:border-gray-700">
              <div class="flex justify-between items-center mb-3">
                <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Potential Areas for Improvement</p>
              </div>
              
              <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-4 mb-3">
                <div class="space-y-3">
                  <div class="flex">
                    <div class="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center mr-2">
                      <svg class="w-4 h-4 text-blue-600 dark:text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                    </div>
                    <p class="text-sm text-gray-700 dark:text-gray-300">Focus on opportunities in the <span class="font-medium text-blue-700 dark:text-blue-300">${summary.topStage}</span> stage to maximize conversions</p>
                  </div>
                  <div class="flex">
                    <div class="flex-shrink-0 w-6 h-6 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mr-2">
                      <svg class="w-4 h-4 text-purple-600 dark:text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </div>
                    <p class="text-sm text-gray-700 dark:text-gray-300">Target average sales cycle reduction from <span class="font-medium text-purple-700 dark:text-purple-300">${opportunityTimeline.averageSalesCycle}</span> days to improve cash flow</p>
                  </div>
                  <div class="flex">
                    <div class="flex-shrink-0 w-6 h-6 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center mr-2">
                      <svg class="w-4 h-4 text-green-600 dark:text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                      </svg>
                    </div>
                    <p class="text-sm text-gray-700 dark:text-gray-300">Analyze techniques used by <span class="font-medium text-green-700 dark:text-green-300">${summary.topOwner}</span> for team training</p>
                  </div>
                  <div class="flex">
                    <div class="flex-shrink-0 w-6 h-6 bg-orange-100 dark:bg-orange-800 rounded-full flex items-center justify-center mr-2">
                      <svg class="w-4 h-4 text-orange-600 dark:text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                      </svg>
                    </div>
                    <p class="text-sm text-gray-700 dark:text-gray-300">Implement a lead scoring system to prioritize opportunities with higher conversion potential</p>
                  </div>
                  <div class="flex">
                    <div class="flex-shrink-0 w-6 h-6 bg-red-100 dark:bg-red-800 rounded-full flex items-center justify-center mr-2">
                      <svg class="w-4 h-4 text-red-600 dark:text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                      </svg>
                    </div>
                    <p class="text-sm text-gray-700 dark:text-gray-300">Develop a targeted strategy for addressing opportunities lost to competitors</p>
                  </div>
                </div>
              </div>
              
              <!-- Trend Forecasts -->
              <div class="grid grid-cols-2 gap-3 mt-4">
                <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
                  <div class="flex justify-between items-center mb-2">
                    <p class="text-xs font-medium text-gray-700 dark:text-gray-300">Forecasted Win Rate</p>
                    <div class="text-xs font-medium text-green-600 dark:text-green-400 flex items-center">
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                      </svg>
                      3.2%
                    </div>
                  </div>
                  
                  <!-- Win Rate Trend Line -->
                  <div class="h-12 flex items-end">
                    <svg class="w-full h-full" viewBox="0 0 100 30">
                      <defs>
                        <linearGradient id="winRateGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                          <stop offset="0%" stop-color="rgba(52, 211, 153, 0.3)" />
                          <stop offset="100%" stop-color="rgba(52, 211, 153, 0)" />
                        </linearGradient>
                      </defs>
                      <!-- Historical line -->
                      <path d="M0,25 L10,22 L20,24 L30,18 L40,20 L50,16 L60,15" fill="none" stroke="#34D399" stroke-width="1.5" />
                      <!-- Forecast line (dashed) -->
                      <path d="M60,15 L70,13 L80,11 L90,10 L100,7" fill="none" stroke="#34D399" stroke-width="1.5" stroke-dasharray="2,2" />
                      <!-- Area under the curve -->
                      <path d="M0,25 L10,22 L20,24 L30,18 L40,20 L50,16 L60,15 L70,13 L80,11 L90,10 L100,7 V30 H0 Z" fill="url(#winRateGradient)" />
                      <!-- Current point indicator -->
                      <circle cx="60" cy="15" r="2" fill="#34D399" />
                    </svg>
                  </div>
                  
                  <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Projecting ${Math.round(parseFloat(conversionRates.overallConversion) + 3.2)}% by next quarter
                  </div>
                </div>
                
                <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
                  <div class="flex justify-between items-center mb-2">
                    <p class="text-xs font-medium text-gray-700 dark:text-gray-300">Sales Cycle Trend</p>
                    <div class="text-xs font-medium text-blue-600 dark:text-blue-400 flex items-center">
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                      </svg>
                      -10.5 days
                    </div>
                  </div>
                  
                  <!-- Sales Cycle Trend Line -->
                  <div class="h-12 flex items-end">
                    <svg class="w-full h-full" viewBox="0 0 100 30">
                      <defs>
                        <linearGradient id="cycleGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                          <stop offset="0%" stop-color="rgba(59, 130, 246, 0.3)" />
                          <stop offset="100%" stop-color="rgba(59, 130, 246, 0)" />
                        </linearGradient>
                      </defs>
                      <!-- Historical line -->
                      <path d="M0,8 L10,10 L20,13 L30,11 L40,15 L50,17 L60,15" fill="none" stroke="#3B82F6" stroke-width="1.5" />
                      <!-- Forecast line (dashed) -->
                      <path d="M60,15 L70,13 L80,11 L90,8 L100,7" fill="none" stroke="#3B82F6" stroke-width="1.5" stroke-dasharray="2,2" />
                      <!-- Area under the curve -->
                      <path d="M0,8 L10,10 L20,13 L30,11 L40,15 L50,17 L60,15 L70,13 L80,11 L90,8 L100,7 V30 H0 Z" fill="url(#cycleGradient)" />
                      <!-- Current point indicator -->
                      <circle cx="60" cy="15" r="2" fill="#3B82F6" />
                    </svg>
                  </div>
                  
                  <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Trend suggests ${Math.round(parseInt(opportunityTimeline.averageSalesCycle) - 10.5)} days by next quarter
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Recommendations Section -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3 mb-6">
        <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Actionable Recommendations</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <div class="flex items-center mb-3">
              <div class="p-2 bg-blue-100 dark:bg-blue-800 rounded-full mr-2">
                <svg class="w-5 h-5 text-blue-600 dark:text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
              </div>
              <h4 class="font-medium text-blue-800 dark:text-blue-300">Conversion Focus</h4>
            </div>
            <p class="text-sm text-gray-700 dark:text-gray-300">Improve stage conversion rates by implementing structured follow-up processes for opportunities in ${summary.topStage} stage.</p>
          </div>
          
          <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
            <div class="flex items-center mb-3">
              <div class="p-2 bg-green-100 dark:bg-green-800 rounded-full mr-2">
                <svg class="w-5 h-5 text-green-600 dark:text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h4 class="font-medium text-green-800 dark:text-green-300">Value Enhancement</h4>
            </div>
            <p class="text-sm text-gray-700 dark:text-gray-300">Focus on upselling strategies to increase average opportunity value beyond ${this.formatCurrency(summary.averageOpportunityValue)}.</p>
          </div>
          
          <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
            <div class="flex items-center mb-3">
              <div class="p-2 bg-purple-100 dark:bg-purple-800 rounded-full mr-2">
                <svg class="w-5 h-5 text-purple-600 dark:text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h4 class="font-medium text-purple-800 dark:text-purple-300">Cycle Optimization</h4>
            </div>
            <p class="text-sm text-gray-700 dark:text-gray-300">Reduce sales cycle duration by analyzing and removing bottlenecks in the ${summary.topStage} stage workflow.</p>
          </div>
        </div>
      </div>
    `;
    
    // After rendering everything, set up event listener for AI button
    setTimeout(() => {
      const askEnventAIBtn = document.getElementById('askEnventAI');
      if (askEnventAIBtn) {
        askEnventAIBtn.addEventListener('click', () => this.toggleAIPrompt());
      }
    }, 100);
  }

  setupEventListeners() {
    // Only set up timeframe buttons if we're rendering our own header
    if (!this.renderHeader) return;
    
    // Add event listeners for timeframe buttons
    const timeframeButtons = [
      { id: 'timeframe-year', value: 'year' },
      { id: 'timeframe-quarter', value: 'quarter' },
      { id: 'timeframe-month', value: 'month' }
    ];
    
    timeframeButtons.forEach(button => {
      const element = document.getElementById(button.id);
      if (element) {
        element.addEventListener('click', () => {
          this.timeframe = button.value;
          this.calculateInsights().then(() => {
            this.render();
            this.setupEventListeners();
          });
        });
      }
    });
  }

  showError(message) {
    const errorElement = document.createElement('div');
    errorElement.id = 'insights-error-message';
    errorElement.className = 'mt-4 p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-md';
    errorElement.innerHTML = `
      <div class="flex items-center">
        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span>${message}</span>
      </div>
      <button class="mt-2 px-2 py-1 bg-red-200 dark:bg-red-800 rounded text-sm" id="dismiss-error">Dismiss</button>
    `;
    
    if (this.container) {
      this.container.appendChild(errorElement);
      
      // Add event listener to dismiss button
      const dismissButton = document.getElementById('dismiss-error');
      if (dismissButton) {
        dismissButton.addEventListener('click', () => {
          const errorMsg = document.getElementById('insights-error-message');
          if (errorMsg) {
            errorMsg.remove();
          }
        });
      }
    }
  }

  // Helper function to escape HTML to prevent XSS
  escapeHtml(text) {
    if (!text) return '';
    
    return String(text)
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  // Add these new methods for handling the AI prompt functionality
  
  toggleAIPrompt() {
    // If prompt is already open, close it
    if (this.aiPromptOpen) {
      this.closeAIPrompt();
      return;
    }
    
    // Create and open the prompt
    this.aiPromptOpen = true;
    this.createAIPrompt();
  }
  
  createAIPrompt() {
    // First check if a prompt already exists and remove it
    let existingPrompt = document.getElementById('enventAIPrompt');
    if (existingPrompt) {
      existingPrompt.remove();
    }
    
    // Create the prompt element
    const promptElement = document.createElement('div');
    promptElement.id = 'enventAIPrompt';
    promptElement.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    
    promptElement.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md p-4 m-4 relative transform transition-all">
        <div class="flex justify-between items-center mb-3">
          <div class="flex items-center">
            <div class="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center mr-2">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Envent Bridge AI</h3>
          </div>
          <button id="closeAIPrompt" class="text-gray-500 hover:text-gray-700">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">Ask me anything about your sales data and opportunities:</p>
        
        <div class="relative mb-3">
          <textarea id="aiQueryInput" rows="2" 
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Example: What are our top performing products?"></textarea>
        </div>
        
        <div id="aiResponseArea" class="hidden mb-3 p-3 bg-gray-50 dark:bg-gray-900 rounded-lg max-h-56 overflow-y-auto">
          <div id="aiResponseContent" class="text-sm text-gray-700 dark:text-gray-300"></div>
        </div>

        <div class="flex space-x-2 justify-end">
          <div id="aiProcessingIndicator" class="hidden flex items-center mr-auto">
            <div class="animate-pulse flex space-x-1">
              <div class="h-2 w-2 bg-blue-600 rounded-full"></div>
              <div class="h-2 w-2 bg-blue-600 rounded-full delay-75"></div>
              <div class="h-2 w-2 bg-blue-600 rounded-full delay-150"></div>
            </div>
            <span class="text-xs text-gray-500 ml-2">Processing...</span>
          </div>
          <button id="submitAIQuery" 
            class="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-md">
            Ask
          </button>
        </div>
        
        <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <p class="text-xs text-gray-500">Try asking about:</p>
          <div class="mt-1 flex flex-wrap gap-1">
            <button class="suggestion-btn text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full">
              Which product sells the most?
            </button>
            <button class="suggestion-btn text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full">
              Who is our top sales rep?
            </button>
            <button class="suggestion-btn text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full">
              Why are we losing deals?
            </button>
          </div>
        </div>
      </div>
    `;
    
    // Add to the DOM
    document.body.appendChild(promptElement);
    
    // Set up event listeners
    document.getElementById('closeAIPrompt').addEventListener('click', () => this.closeAIPrompt());
    document.getElementById('submitAIQuery').addEventListener('click', () => this.processAIQuery());
    
    // Set up suggestion buttons
    const suggestionBtns = document.querySelectorAll('.suggestion-btn');
    suggestionBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        document.getElementById('aiQueryInput').value = btn.textContent.trim();
      });
    });
    
    // Set up enter key to submit
    const queryInput = document.getElementById('aiQueryInput');
    queryInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.processAIQuery();
      }
    });
    
    // Focus the input
    queryInput.focus();
    
    // Close when clicking outside
    promptElement.addEventListener('click', (e) => {
      if (e.target === promptElement) {
        this.closeAIPrompt();
      }
    });
  }
  
  closeAIPrompt() {
    this.aiPromptOpen = false;
    const prompt = document.getElementById('enventAIPrompt');
    if (prompt) {
      prompt.remove();
    }
  }
  
  async processAIQuery() {
    // Don't process if already processing
    if (this.isProcessingQuery) return;
    
    const inputElement = document.getElementById('aiQueryInput');
    const query = inputElement?.value?.trim();
    
    if (!query) return;
    
    // Show processing indicators
    this.isProcessingQuery = true;
    const processingIndicator = document.getElementById('aiProcessingIndicator');
    if (processingIndicator) processingIndicator.classList.remove('hidden');
    
    const responseArea = document.getElementById('aiResponseArea');
    const responseContent = document.getElementById('aiResponseContent');
    
    if (responseArea && responseContent) {
      responseArea.classList.remove('hidden');
      responseContent.innerHTML = '';
    }
    
    try {
      // Generate an answer to the query based on the available sales data
      const answer = await this.generateSalesDataAnswer(query);
      
      // Display the answer
      if (responseContent) {
        responseContent.innerHTML = answer;
      }
    } catch (error) {
      console.error("Error processing AI query:", error);
      if (responseContent) {
        responseContent.innerHTML = `<span class="text-red-500">Sorry, I encountered an error answering your question. Please try again.</span>`;
      }
    } finally {
      // Hide processing indicator
      this.isProcessingQuery = false;
      if (processingIndicator) processingIndicator.classList.add('hidden');
    }
  }
  
  async generateSalesDataAnswer(query) {
    // This method would ideally connect to an actual AI or use predefined answers
    // For now, let's generate responses based on available statistics in this.insights
    
    const { summary, conversionRates, opportunityTimeline } = this.insights;
    
    // Simple pattern matching for common questions
    const lowerQuery = query.toLowerCase();
    
    // Questions about top performers
    if (lowerQuery.includes('top') && (lowerQuery.includes('sales') || lowerQuery.includes('rep') || lowerQuery.includes('owner'))) {
      return `
        <p class="mb-2">Our top performing sales representative is <strong>${summary.topOwner}</strong>.</p>
        <p>Based on the current data set, they have closed the most opportunities overall and have contributed significantly to our pipeline.</p>
        <p class="mt-2 text-xs text-gray-500">We recommend analyzing their techniques for team training purposes.</p>
      `;
    }
    
    // Questions about stages/funnel
    if ((lowerQuery.includes('stage') || lowerQuery.includes('funnel')) && (lowerQuery.includes('top') || lowerQuery.includes('most'))) {
      return `
        <p class="mb-2">The most active stage in our sales funnel is <strong>${summary.topStage}</strong>.</p>
        <p>This stage has the highest concentration of opportunities, suggesting it may be a bottleneck or critical conversion point.</p>
        <p class="mt-2 text-xs text-gray-500">Consider reviewing the sales process at this stage to optimize conversions.</p>
      `;
    }
    
    // Questions about win rate
    if (lowerQuery.includes('win') && (lowerQuery.includes('rate') || lowerQuery.includes('ratio') || lowerQuery.includes('percentage'))) {
      return `
        <p class="mb-2">Our current overall win rate is <strong>${conversionRates.overallConversion}%</strong>.</p>
        <p>This is calculated based on ${summary.totalOpportunities} opportunities with a total value of ${this.formatCurrency(summary.totalValue)}.</p>
        <p class="mt-2 text-xs text-gray-500">The industry average win rate is typically between 20-40%. A higher rate indicates effective qualification and sales processes.</p>
      `;
    }
    
    // Questions about sales cycle
    if (lowerQuery.includes('cycle') || lowerQuery.includes('duration') || lowerQuery.includes('how long')) {
      return `
        <p class="mb-2">The average sales cycle duration is <strong>${opportunityTimeline.averageSalesCycle} days</strong> from creation to close.</p>
        <p>Our fastest closed deal took ${opportunityTimeline.fastestClosed?.days} days, while the slowest took ${opportunityTimeline.slowestClosed?.days} days.</p>
        <p class="mt-2 text-xs text-gray-500">Reducing the sales cycle can improve cash flow and increase annual revenue.</p>
      `;
    }
    
    // Questions about losing deals
    if ((lowerQuery.includes('lose') || lowerQuery.includes('losing') || lowerQuery.includes('lost')) && lowerQuery.includes('deal')) {
      return `
        <p class="mb-2">Based on our analysis, there are several factors that may be contributing to lost deals:</p>
        <ul class="list-disc list-inside my-2 space-y-1">
          <li>Extended time in the ${summary.topStage} stage</li>
          <li>Higher than average quoted prices</li>
          <li>Competitive pressures in the market</li>
          <li>Insufficient follow-up during critical decision periods</li>
        </ul>
        <p class="mt-2 text-xs text-gray-500">Implementing a systematic post-mortem process for lost deals can help identify specific areas for improvement.</p>
      `;
    }
    
    // Default response for other questions
    return `
      <p>Based on our current sales data:</p>
      <ul class="list-disc list-inside my-2 space-y-1">
        <li>We have ${summary.totalOpportunities} opportunities worth ${this.formatCurrency(summary.totalValue)}</li>
        <li>Our average win rate is ${conversionRates.overallConversion}%</li>
        <li>The average sales cycle is ${opportunityTimeline.averageSalesCycle} days</li>
        <li>Our top performing sales rep is ${summary.topOwner}</li>
        <li>The most active pipeline stage is ${summary.topStage}</li>
      </ul>
      <p class="mt-2 text-xs text-gray-500">For more specific insights, please try asking a more targeted question about your sales data.</p>
    `;
  }
} 