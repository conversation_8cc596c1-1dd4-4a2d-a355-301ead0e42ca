// Sales Order component for Sales KPI Dashboard
import { connectionManager } from '../../core/connection.js';
import { NotificationSystem } from '../../core/notifications.js';

export class SalesOrderComponent {
  constructor(container) {
    this.container = container;
    this.salesOrders = [];
    this.filteredSalesOrders = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'orderNbr';
    this.sortDirection = 'desc';
    this.filterStatus = 'all';
    this.isLoading = true;
    this.dbName = 'salesKpiDb';
    this.storeName = 'salesOrders';
    this.dateRange = {
      start: null,
      end: null
    };
    this.notificationSystem = new NotificationSystem();
  }

  async init() {
    console.log("Initializing Sales Order component");
    
    this.isLoading = true;
    this.render();
    
    try {
      // Initialize IndexedDB
      await this.initDatabase();
      
      // Load sales order data
      await this.loadData();
    
      // Update loading state and render again
      this.isLoading = false;
      this.render();
    
      // Set up event listeners
      this.setupEventListeners();
      
    } catch (error) {
      console.error("Error initializing sales orders:", error);
      this.isLoading = false;
      this.showError("Failed to initialize: " + error.message);
      this.render();
    }
  }

  async initDatabase() {
    return new Promise((resolve, reject) => {
      // Try to open with current version first (don't upgrade yet)
      const checkRequest = indexedDB.open(this.dbName);
      
      checkRequest.onsuccess = (event) => {
        const db = event.target.result;
        const currentVersion = db.version;
        db.close();
        
        // Now open with the correct version based on what we found
        const version = Math.max(currentVersion, 1);
        
        const request = indexedDB.open(this.dbName, version);

        request.onerror = (event) => {
          console.error("Error opening IndexedDB:", event.target.error);
          reject(new Error("Could not open sales orders database"));
        };

        request.onsuccess = (event) => {
          console.log("Successfully opened sales orders database");
          resolve();
        };

        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          
          // Create object store for sales orders if it doesn't exist
          if (!db.objectStoreNames.contains(this.storeName)) {
            const store = db.createObjectStore(this.storeName, { keyPath: "id" });
            
            // Create indices for common search and sort operations
            store.createIndex("orderNbr", "orderNbr", { unique: false });
            store.createIndex("date", "date", { unique: false });
            store.createIndex("customerID", "customerID", { unique: false });
            store.createIndex("status", "status", { unique: false });
            store.createIndex("orderTotal", "orderTotal", { unique: false });
          }
          
          console.log("Sales orders database schema upgraded to version", db.version);
        };
      };
      
      checkRequest.onerror = (event) => {
        console.error("Error checking database version:", event.target.error);
        reject(new Error("Could not check database version"));
      };
    });
  }

  async loadData(forceRefresh = false) {
    try {
      this.isLoading = true;
      
      // Check connection status
      const connectionStatus = connectionManager.getConnectionStatus();
      
      // When forceRefresh is true, always try to fetch from Acumatica first if connected
      if (forceRefresh && connectionStatus.acumatica.isConnected) {
        console.log("Force refreshing - fetching latest sales orders from Acumatica");
        try {
          const result = await this.fetchAcumaticaSalesOrders(connectionStatus.acumatica.instance);
          
          if (result.success) {
            // Parse the data and store in IndexedDB
            this.salesOrders = this.parseAcumaticaSalesOrders(result.data);
            await this.storeSalesOrdersInIndexedDB(this.salesOrders);
            console.log(`Refreshed and stored ${this.salesOrders.length} sales orders in IndexedDB`);
            
            // Continue with filtering
            this.filteredSalesOrders = [...this.salesOrders];
            this.calculateTotalPages();
            this.isLoading = false;
            return;
          } else {
            console.warn("Error refreshing from Acumatica:", result.error);
            // Fall through to normal loading behavior as fallback
          }
        } catch (fetchError) {
          console.error("Error refreshing sales orders from Acumatica:", fetchError);
          // Fall through to normal loading behavior as fallback
        }
      }
      
      // If not forcing refresh or Acumatica refresh failed, follow normal loading flow
      // If not connected to Acumatica, or if offline and we have data in IndexedDB
      if (!connectionStatus.acumatica.isConnected || (!forceRefresh && !navigator.onLine)) {
        // Try to get data from IndexedDB
        this.salesOrders = await this.getSalesOrdersFromIndexedDB();
        
        // If no data in IndexedDB, show empty state
        if (this.salesOrders.length === 0) {
          console.log("No sales orders in IndexedDB and not connected to Acumatica");
          this.salesOrders = [];
        }
      } else {
        // Connected to Acumatica, fetch real data
        console.log("Fetching sales orders from Acumatica");
        try {
          const result = await this.fetchAcumaticaSalesOrders(connectionStatus.acumatica.instance);
          
          if (result.success) {
            // Parse the data and store in IndexedDB
            this.salesOrders = this.parseAcumaticaSalesOrders(result.data);
            await this.storeSalesOrdersInIndexedDB(this.salesOrders);
            console.log(`Stored ${this.salesOrders.length} sales orders in IndexedDB`);
          } else {
            // If error fetching from Acumatica, try IndexedDB
            console.warn("Error fetching from Acumatica, trying IndexedDB:", result.error);
            this.salesOrders = await this.getSalesOrdersFromIndexedDB();
          }
        } catch (fetchError) {
          console.error("Error fetching sales orders from Acumatica:", fetchError);
          // Try to get data from IndexedDB as fallback
          this.salesOrders = await this.getSalesOrdersFromIndexedDB();
        }
      }
      
      // Apply filters
      this.filteredSalesOrders = [...this.salesOrders];
      this.calculateTotalPages();
      
      this.isLoading = false;
    } catch (error) {
      console.error('Error loading sales order data:', error);
      this.salesOrders = [];
      this.filteredSalesOrders = [];
      this.calculateTotalPages();
      this.isLoading = false;
    }
  }

  // Fetch sales orders from Acumatica API
  async fetchAcumaticaSalesOrders(instance) {
    try {
      // Build Acumatica API URL for sales orders
      const apiUrl = `${instance}/entity/default/22.200.001/SalesOrder?$expand=Details,Shipments&$filter=Date ge datetimeoffset'2024-01-01T00:00:00Z' and Date lt datetimeoffset'2026-01-01T00:00:00Z'`;
      
      console.log("Fetching sales orders with URL:", apiUrl);
      
      // Make request with cookies through the connection manager
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        credentials: 'include'  // Include cookies for authentication
      });
      
      // Check response
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please reconnect to Acumatica.');
        }
        throw new Error(`Failed to fetch sales orders: ${response.status} ${response.statusText}`);
      }
      
      // Parse response
      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error("Error fetching sales orders from Acumatica:", error);
      return { success: false, error: error.message };
    }
  }

  parseAcumaticaSalesOrders(salesOrderData) {
    try {
      // Process sales order data from Acumatica
      return salesOrderData.map(order => {
        // Function to parse date string and normalize to UTC midnight
        const parseAndNormalizeDate = (dateString) => {
          if (!dateString || typeof dateString !== 'string') return null;
          
          try {
            // Extract YYYY-MM-DD part
            const datePart = dateString.substring(0, 10); // e.g., "2025-05-21"
            
            // Validate the format (basic check)
            if (!/^\d{4}-\d{2}-\d{2}$/.test(datePart)) {
                console.warn(`Invalid date format extracted: ${datePart} from ${dateString}`);
                return null;
            }

            // Create a Date object representing midnight UTC for that day
            const date = new Date(`${datePart}T00:00:00.000Z`); 
            
            if (isNaN(date.getTime())) {
              console.warn(`Invalid date created from string: ${datePart}T00:00:00.000Z`);
              return null;
            }
            return date;
          } catch(e) {
            console.error(`Error parsing date: ${dateString}`, e);
            return null;
          }
        };

        // Extract main sales order fields
        const id = order.id;
        const orderNbr = order.OrderNbr?.value || '';
        const orderType = order.OrderType?.value || 'SO';
        const date = parseAndNormalizeDate(order.Date?.value);
        const status = order.Status?.value || '';
        const customerID = order.CustomerID?.value || '';
        const customerName = order.CustomerName?.value || customerID;
        const orderTotal = parseFloat(order.OrderTotal?.value || 0);
        const currencyID = order.CurrencyID?.value || 'USD';
        const lastModified = parseAndNormalizeDate(order.LastModifiedDateTime?.value);
        
        // Extract line items
        const details = order.Details || [];
        const itemCount = details.length;
        
        // Extract shipments
        const shipments = (order.Shipments || []).map(shipment => ({
          id: shipment.id || `SH${Math.random().toString(36).substr(2, 9)}`,
          shipmentNbr: shipment.ShipmentNbr?.value || 'N/A',
          shipmentDate: shipment.ShipmentDate?.value ? parseAndNormalizeDate(shipment.ShipmentDate.value) : null,
          status: shipment.Status?.value || 'Unknown'
        }));
        
        // Parse into our standard sales order format
        return {
          id,
          orderNbr: orderNbr,
          orderType: orderType,
          date: date,
          formattedDate: date ? date.toLocaleDateString() : 'N/A',
          status: status,
          customerID: customerID,
          customerName: customerName,
          orderTotal: orderTotal,
          formattedTotal: this.formatCurrency(orderTotal, currencyID),
          currencyID: currencyID,
          itemCount: itemCount,
          lastModified: lastModified,
          shipments: shipments,
          details: details.map(item => ({
            id: item.id || `SOLN${Math.random().toString(36).substr(2, 9)}`,
            lineNbr: item.LineNbr?.value || 0,
            inventoryID: item.InventoryID?.value || '',
            description: item.LineDescription?.value || '',
            quantity: item.OrderQty?.value || 0,
            unitPrice: item.UnitPrice?.value || 0,
            extendedPrice: item.ExtendedPrice?.value || 0,
            warehouse: item.WarehouseID?.value || '',
            uom: item.UOM?.value || ''
          })),
          // Store the full original data for detailed view
          rawData: order
        };
      });
    } catch (error) {
      console.error("Error parsing Acumatica sales orders:", error);
      return [];
    }
  }

  async getSalesOrdersFromIndexedDB() {
    return new Promise((resolve, reject) => {
      // Open without specifying version to use existing version
      const request = indexedDB.open(this.dbName);
      
      request.onerror = (event) => {
        console.error("Error opening database:", event.target.error);
        resolve([]); // Resolve with empty array instead of rejecting
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        console.log("Successfully opened database for reading, version:", db.version);
        
        // Check if the store exists
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.close();
          resolve([]); // Resolve with empty array instead of rejecting
          return;
        }
        
        const transaction = db.transaction([this.storeName], "readonly");
        const store = transaction.objectStore(this.storeName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          const salesOrders = getAllRequest.result;
          // Parse stored date strings back into Date objects
          salesOrders.forEach(order => {
            try {
               const parseStoredDate = (dateStr) => {
                 if (!dateStr || typeof dateStr !== 'string' || !/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                   return null;
                 }
                 const date = new Date(`${dateStr}T00:00:00.000Z`);
                 return isNaN(date.getTime()) ? null : date;
               };

               order.date = parseStoredDate(order.date);
               order.lastModified = parseStoredDate(order.lastModified);
               
               // Ensure orderTotal is a number
               order.orderTotal = parseFloat(order.orderTotal) || 0;
               
               // Update formatted date
               order.formattedDate = order.date ? order.date.toLocaleDateString() : 'N/A';
            } catch (dateError) {
               console.warn(`Error parsing stored dates for Sales Order ${order.orderNbr}:`, dateError);
               order.date = null;
               order.formattedDate = 'N/A';
            }
          });
          
          console.log(`Retrieved ${salesOrders.length} sales orders from IndexedDB`);
          resolve(salesOrders);
        };
        
        getAllRequest.onerror = (event) => {
          console.error("Error retrieving sales orders:", event.target.error);
          resolve([]); // Resolve with empty array instead of rejecting
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
        
        transaction.onerror = (event) => {
          console.error("Transaction error retrieving sales orders:", event.target.error);
          db.close();
        };
      };
    });
  }

  async storeSalesOrdersInIndexedDB(salesOrders) {
    return new Promise((resolve, reject) => {
      // Open without specifying version to use existing version
      const request = indexedDB.open(this.dbName);
      
      request.onerror = (event) => {
        console.error("Error opening database for storing:", event.target.error);
        resolve(); // Resolve anyway to avoid blocking
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        console.log("Successfully opened database for writing, version:", db.version);
        
        // Check if the store exists
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.close();
          resolve(); // Resolve anyway to avoid blocking
          return;
        }
        
        const transaction = db.transaction([this.storeName], "readwrite");
        const store = transaction.objectStore(this.storeName);
        let count = 0;
        let errorCount = 0;
        const totalOrders = salesOrders.length;
        
        // Clear existing data if this is a fresh load
        store.clear().onsuccess = () => {
           console.log(`Cleared existing sales order data. Storing ${totalOrders} new sales orders.`);
           if (totalOrders === 0) {
               resolve(); // Nothing more to do
               db.close();
               return;
           }

          // Add each sales order, converting dates to YYYY-MM-DD strings for storage
          salesOrders.forEach(originalOrder => {
             // Create a copy to avoid modifying the original object in memory
             const orderToStore = { ...originalOrder };

             // Convert Date objects to YYYY-MM-DD strings
             try {
               const formatDateToUTCString = (dateObj) => {
                   if (!(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
                       return null;
                   }
                   const year = dateObj.getUTCFullYear();
                   const month = (dateObj.getUTCMonth() + 1).toString().padStart(2, '0');
                   const day = dateObj.getUTCDate().toString().padStart(2, '0');
                   return `${year}-${month}-${day}`;
               };

               orderToStore.date = formatDateToUTCString(orderToStore.date);
               orderToStore.lastModified = formatDateToUTCString(orderToStore.lastModified);
               
               // Ensure orderTotal is a number
               orderToStore.orderTotal = parseFloat(orderToStore.orderTotal) || 0;

             } catch (conversionError) {
                 console.error(`Error converting dates to string for Sales Order ${orderToStore.orderNbr}:`, conversionError);
                 errorCount++;
                 if (count + errorCount === totalOrders) { 
                   db.close();
                   resolve(); 
                 }
             }
            
            const addRequest = store.add(orderToStore);
            
            addRequest.onsuccess = () => {
              count++;
              if (count + errorCount === totalOrders) {
                console.log(`Successfully stored ${count} sales orders.`);
                db.close();
                resolve();
              }
            };
            
            addRequest.onerror = (event) => {
              console.error("Error storing sales order:", orderToStore.orderNbr, event.target.error);
              errorCount++;
              if (count + errorCount === totalOrders) {
                 console.warn(`Finished storing with ${errorCount} errors.`);
                 db.close();
                 resolve();
              }
            };
          });
        };
        
        store.clear().onerror = (event) => {
          console.error("Error clearing existing sales order data:", event.target.error);
          db.close();
          resolve(); // Resolve anyway to avoid blocking
        };
        
        transaction.onerror = (event) => {
          console.error("Transaction error storing sales orders:", event.target.error);
          db.close();
          resolve(); // Resolve anyway to avoid blocking
        };
      };
    });
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredSalesOrders.length / this.itemsPerPage));
    
    // Adjust current page if it's out of bounds
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading sales orders...</p>
      </div>
    `;
  }

  renderContent() {
    // Clear container
    this.container.innerHTML = '';
    
    // Create content area
    const contentElement = document.createElement('div');
    contentElement.className = 'bg-white dark:bg-gray-800 rounded-lg shadow p-4';
    this.container.appendChild(contentElement);
    
    // Render the header and controls
    this.renderHeader(contentElement);
    
    // Render the table
    this.renderTable(contentElement);
    
    // Set up event listeners
    this.setupEventListeners();
  }
  
  renderHeader(container) {
    // Create and append the header with controls matching opportunity.js style
    const headerElement = document.createElement('div');
    headerElement.className = 'flex flex-col md:flex-row justify-between items-center mb-6'; 
    
    headerElement.innerHTML = `
      <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4 md:mb-0">Sales Orders</h2>
          
      <div class="flex flex-wrap items-center gap-2">
        <div class="relative">
          <input 
            type="text" 
            id="sales-order-search" 
            placeholder="Search sales orders..." 
            class="w-full sm:w-64 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
            value="${this.searchTerm || ''}"
          >
          <button id="clear-search" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${!this.searchTerm ? 'hidden' : ''}">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <select id="status-filter" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm">
          <option value="all" ${this.filterStatus === 'all' ? 'selected' : ''}>All Statuses</option>
          <option value="open" ${this.filterStatus === 'open' ? 'selected' : ''}>Open</option>
          <option value="closed" ${this.filterStatus === 'closed' ? 'selected' : ''}>Closed</option>
          <option value="cancelled" ${this.filterStatus === 'cancelled' ? 'selected' : ''}>Cancelled</option>
          <option value="on hold" ${this.filterStatus === 'on hold' ? 'selected' : ''}>On Hold</option>
          <option value="credit hold" ${this.filterStatus === 'credit hold' ? 'selected' : ''}>Credit Hold</option>
        </select>
        
        <div class="flex gap-2">
          <!-- Date Range Button -->
          <button id="date-range-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Date Range">
            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </button>
          
          <!-- Refresh Button -->
          <button id="refresh-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Refresh Data">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          </button>
          
          <!-- Export Button -->
          <button id="export-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Export Data">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
            </svg>
          </button>
        </div>
      </div>
    `;
    
    container.appendChild(headerElement);
  }

  renderTable(container) {
    // Create the sales order table matching opportunity.js style
    const tableElement = document.createElement('div');
    tableElement.innerHTML = `
      <!-- Sales Order Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="orderNbr">
                Order # <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="date">
                Date <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="customerName">
                Customer <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="status">
                Status <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="orderTotal">
                Total Amount <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="itemCount">
                Items <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            ${this.renderTableRows()}
          </tbody>
        </table>
      </div>

      <!-- Pagination matching opportunity.js style -->
      <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredSalesOrders.length)} to 
          ${Math.min(this.currentPage * this.itemsPerPage, this.filteredSalesOrders.length)} of 
          ${this.filteredSalesOrders.length} results
        </div>
        
        <div class="flex items-center space-x-1">
          <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-double-left"></i>
          </button>
          <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-left"></i>
          </button>
          
          <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
            ${this.currentPage} of ${this.totalPages}
          </span>
          
          <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-right"></i>
          </button>
          <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-double-right"></i>
          </button>
        </div>
      </div>
    `;
    
    container.appendChild(tableElement);
  }

  renderTableRows() {
    if (this.filteredSalesOrders.length === 0) {
      return `
        <tr>
          <td colspan="7" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
            No sales orders found matching your criteria
          </td>
        </tr>
      `;
    }

    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = Math.min(start + this.itemsPerPage, this.filteredSalesOrders.length);
    const displayedOrders = this.filteredSalesOrders.slice(start, end);

    return displayedOrders.map(order => `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
        <td class="px-3 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400 font-medium">
          ${this.escapeHtml(order.orderNbr)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${order.formattedDate}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">
          ${this.escapeHtml(order.customerName)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <span class="px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusClass(order.status)}">
            ${this.escapeHtml(order.status)}
          </span>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${order.formattedTotal}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${order.itemCount}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
          <button data-id="${order.id}" class="view-order text-blue-600 hover:text-blue-900 dark:hover:text-blue-400 mr-3">
            <i class="fas fa-eye"></i>
          </button>
          <button data-id="${order.id}" class="print-order text-blue-600 hover:text-blue-900 dark:hover:text-blue-400">
            <i class="fas fa-print"></i>
          </button>
        </td>
      </tr>
    `).join('');
  }

  setupEventListeners() {
    // Search input
    const searchInput = document.getElementById('sales-order-search');
    if (searchInput) {
      searchInput.addEventListener('input', this.debounce(() => {
        this.searchTerm = searchInput.value.trim();
        this.currentPage = 1;
        this.applyFilters();
      }, 300));
    }

    // Clear search
    const clearSearchBtn = document.getElementById('clear-search');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        this.searchTerm = '';
        if (searchInput) searchInput.value = '';
        this.currentPage = 1;
        this.applyFilters();
      });
    }

    // Status filter
    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
      statusFilter.addEventListener('change', () => {
        this.filterStatus = statusFilter.value;
        this.currentPage = 1;
        this.applyFilters();
      });
    }
    
    // Refresh button
    const refreshButton = document.getElementById('refresh-button');
    if (refreshButton) {
      refreshButton.addEventListener('click', () => {
        this.refreshData();
      });
    }

    // Export button
    const exportButton = document.getElementById('export-button');
    if (exportButton) {
      exportButton.addEventListener('click', () => {
        this.exportSalesOrderData();
      });
    }

    // Date Range button
    const dateRangeButton = document.getElementById('date-range-button');
    if (dateRangeButton) {
      dateRangeButton.addEventListener('click', () => {
        this.showDateRangePicker();
      });
    }

    // Sort headers
    const sortHeaders = document.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'asc';
        }
        this.applyFilters();
      });
    });

    // Pagination event handlers
    const firstPageBtn = document.getElementById('first-page');
    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage = 1;
          this.renderContent();
        }
      });
    }

    const prevPageBtn = document.getElementById('prev-page');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.renderContent();
        }
      });
    }

    const nextPageBtn = document.getElementById('next-page');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.renderContent();
        }
      });
    }

    const lastPageBtn = document.getElementById('last-page');
    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage = this.totalPages;
          this.renderContent();
        }
      });
    }

    // View order buttons
    const viewButtons = document.querySelectorAll('.view-order');
    viewButtons.forEach(button => {
      button.addEventListener('click', () => {
        const orderId = button.getAttribute('data-id');
        this.viewOrderDetails(orderId);
      });
    });

    // Print order buttons
    const printButtons = document.querySelectorAll('.print-order');
    printButtons.forEach(button => {
      button.addEventListener('click', () => {
        const orderId = button.getAttribute('data-id');
        this.printOrder(orderId);
      });
    });
  }

  applyFilters() {
    // Filter by search term
    let filtered = this.salesOrders;
    
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(order => 
        (order.orderNbr?.toLowerCase().includes(term)) ||
        (order.customerName?.toLowerCase().includes(term)) ||
        (order.customerID?.toLowerCase().includes(term)) ||
        (order.status?.toLowerCase().includes(term))
      );
    }
    
    // Filter by status
    if (this.filterStatus !== 'all') {
      filtered = filtered.filter(order => 
        order.status?.toLowerCase() === this.filterStatus.toLowerCase()
      );
    }
    
    // Filter by date range if specified
    if (this.dateRange.start && this.dateRange.end) {
      const startDate = new Date(this.dateRange.start);
      startDate.setHours(0, 0, 0, 0);
      const endDate = new Date(this.dateRange.end);
      endDate.setHours(23, 59, 59, 999);
      
      filtered = filtered.filter(order => {
        if (order.date instanceof Date && !isNaN(order.date.getTime())) {
          return order.date >= startDate && order.date <= endDate;
        }
        return false;
      });
    }
    
    // Sort the data
    filtered.sort((a, b) => {
      let comparison = 0;
      const fieldA = a[this.sortField];
      const fieldB = b[this.sortField];

      // Handle null/undefined values
      const valA = fieldA ?? '';
      const valB = fieldB ?? '';

      switch (this.sortField) {
        case 'orderNbr':
          // Parse orderNbr as integers for proper numerical sorting
          const idA = parseInt(valA.replace(/\D/g, '')) || 0;
          const idB = parseInt(valB.replace(/\D/g, '')) || 0;
          comparison = idA - idB;
          break;
        case 'customerName':
        case 'status':
          comparison = String(valA).localeCompare(String(valB));
          break;
        case 'orderTotal':
        case 'itemCount':
          comparison = parseFloat(valA) - parseFloat(valB);
          break;
        case 'date':
          // Ensure valid dates before comparing
          const dateA = (valA instanceof Date && !isNaN(valA)) ? valA.getTime() : 0;
          const dateB = (valB instanceof Date && !isNaN(valB)) ? valB.getTime() : 0;
          comparison = dateA - dateB;
          break;
        default:
          comparison = String(valA).localeCompare(String(valB));
      }

      return this.sortDirection === 'asc' ? comparison : -comparison;
    });
    
    this.filteredSalesOrders = filtered;
    this.calculateTotalPages();
    this.renderContent();
  }

  // Handle refresh action
  refreshData() {
    // Force refresh from Acumatica
    this.loadData(true)
      .then(() => {
        this.notificationSystem.addNotification("Sales order data refreshed successfully", "success");
      })
      .catch(error => {
        this.notificationSystem.addNotification("Error refreshing data: " + error.message, "error");
      });
  }

  formatCurrency(amount, currencyCode = 'USD') {
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: currencyCode
    }).format(amount);
  }

  getStatusClass(status) {
    switch (status.toLowerCase()) {
      case 'open':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'closed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'on hold':
      case 'credit hold':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  }

  escapeHtml(text) {
    if (!text) return '';
    
    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  debounce(func, wait) {
    let timeout;
    return function(...args) {
      const context = this;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), wait);
    };
  }

  showDateRangePicker() {
    // Create date range picker modal
    const today = new Date();
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(today.getMonth() - 1);
    
    // Format dates for input fields
    const formatDateForInput = (date) => {
      if (!date) return '';
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };
    
    const startDateValue = this.dateRange.start ? formatDateForInput(this.dateRange.start) : formatDateForInput(oneMonthAgo);
    const endDateValue = this.dateRange.end ? formatDateForInput(this.dateRange.end) : formatDateForInput(today);
    
    const modalContent = `
      <div class="p-6">
        <h3 class="text-lg font-medium mb-4">Select Date Range</h3>
        
        <div class="mb-6">
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Filter sales orders by their order date. Only orders created within this date range will be displayed.
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Start Date</label>
              <input 
                type="date" 
                id="date-range-start" 
                class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"
                value="${startDateValue}"
              >
            </div>
            
            <div>
              <label class="block text-sm font-medium mb-1">End Date</label>
              <input 
                type="date" 
                id="date-range-end" 
                class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"
                value="${endDateValue}"
              >
            </div>
          </div>
        </div>
        
        <div class="flex justify-between">
          <div>
            <button id="date-range-clear" class="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md">
              Clear Filter
            </button>
          </div>
          <div class="flex gap-2">
            <button id="date-range-cancel" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
              Cancel
            </button>
            <button id="date-range-apply" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
              Apply Filter
            </button>
          </div>
        </div>
      </div>
    `;
    
    // Create modal dialog
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalOverlay.id = 'date-range-modal';
    
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full';
    modalContainer.innerHTML = modalContent;
    
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);
    
    // Setup event listeners for the modal
    const cancelButton = document.getElementById('date-range-cancel');
    const applyButton = document.getElementById('date-range-apply');
    const clearButton = document.getElementById('date-range-clear');
    const startDateInput = document.getElementById('date-range-start');
    const endDateInput = document.getElementById('date-range-end');
    
    const closeModal = () => {
      document.body.removeChild(modalOverlay);
    };
    
    if (cancelButton) {
      cancelButton.addEventListener('click', closeModal);
    }
    
    if (clearButton) {
      clearButton.addEventListener('click', () => {
        this.dateRange = {
          start: null,
          end: null
        };
        
        this.currentPage = 1;
        this.applyFilters();
        
        this.notificationSystem.addNotification("Date filter cleared", "success");
        closeModal();
      });
    }
    
    if (applyButton) {
      applyButton.addEventListener('click', () => {
        // Get selected dates
        const startDate = startDateInput.value ? new Date(startDateInput.value) : null;
        const endDate = endDateInput.value ? new Date(endDateInput.value) : null;
        
        // Validate dates
        if (startDate && endDate && startDate > endDate) {
          this.notificationSystem.addNotification("Start date cannot be after end date", "error");
          return;
        }
        
        // Apply date filter
        this.dateRange = {
          start: startDate,
          end: endDate
        };
        
        this.currentPage = 1;
        this.applyFilters();
        
        if (startDate && endDate) {
          this.notificationSystem.addNotification(`Filtered sales orders from ${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}`, "success");
        }
        closeModal();
      });
    }
  }

  exportSalesOrderData() {
    try {
      // Create CSV content
      let csvContent = 'data:text/csv;charset=utf-8,';
      csvContent += 'Order #,Date,Customer,Status,Total Amount,Items\n';
      
      // Add each row of data
      this.filteredSalesOrders.forEach(order => {
        const row = [
          order.orderNbr,
          order.formattedDate,
          order.customerName,
          order.status,
          order.formattedTotal,
          order.itemCount
        ].map(cell => `"${cell}"`).join(',');
        
        csvContent += row + '\n';
      });
      
      // Create download link
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement('a');
      link.setAttribute('href', encodedUri);
      link.setAttribute('download', `sales_orders_${new Date().toISOString().slice(0, 10)}.csv`);
      document.body.appendChild(link);
      
      // Trigger download
      link.click();
      document.body.removeChild(link);
      
      this.notificationSystem.addNotification("Sales order data exported successfully", "success");
    } catch (error) {
      console.error('Error exporting data:', error);
      this.notificationSystem.addNotification("Failed to export data: " + error.message, "error");
    }
  }

  viewOrderDetails(orderId) {
    const order = this.salesOrders.find(o => o.id === orderId);
    if (!order) {
      console.error('Order not found:', orderId);
      return;
    }
    
    // Remove any existing modal first
    const existingModal = document.getElementById('order-details-modal');
    if (existingModal) {
      document.body.removeChild(existingModal);
    }
    
    // Build the modal content - matching opportunity.js structure exactly
    const modalContent = `
      <div class="modal-content overflow-y-auto" style="max-height: 70vh;">
      <div class="p-6">
        <div class="flex flex-col md:flex-row justify-between mb-6">
          <h2 class="text-xl font-semibold mb-2">Sales Order ${this.escapeHtml(order.orderNbr)}</h2>
          <div class="flex items-center">
            <span class="px-2 py-1 text-sm font-semibold rounded-full ${this.getStatusClass(order.status)}">
              ${this.escapeHtml(order.status)}
            </span>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- Order Details -->
          <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Order Details</h3>
            <div class="space-y-2">
              <p class="text-sm flex justify-between">
                <span class="font-medium">Order Number:</span> 
                <span>${this.escapeHtml(order.orderNbr)}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Order Type:</span> 
                <span>${this.escapeHtml(order.orderType)}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Date:</span> 
                <span>${order.formattedDate}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Status:</span> 
                <span>${this.escapeHtml(order.status)}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Total Amount:</span> 
                <span>${order.formattedTotal}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Currency:</span> 
                <span>${this.escapeHtml(order.currencyID || 'USD')}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Item Count:</span> 
                <span>${order.itemCount}</span>
              </p>
            </div>
          </div>

          <!-- Customer Information -->
          <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Customer Information</h3>
            <div class="space-y-2">
              <p class="text-sm flex justify-between">
                <span class="font-medium">Customer:</span> 
                <span>${this.escapeHtml(order.customerName)}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Customer ID:</span> 
                <span>${this.escapeHtml(order.customerID)}</span>
              </p>
            </div>
          </div>
        </div>

        <!-- Line Items -->
        <div class="mt-6">
          <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Line Items (${order.details.length})</h3>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Line</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Item #</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
                  <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Qty</th>
                  <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Unit Price</th>
                  <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Extended</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${order.details && order.details.length > 0 ? order.details.map((item) => `
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${item.lineNbr || 0}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${this.escapeHtml(item.inventoryID || '')}</td>
                    <td class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                      <div class="line-clamp-2">${this.escapeHtml(item.description || '')}</div>
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${item.quantity || 0} ${item.uom || ''}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${this.formatCurrency(item.unitPrice || 0, order.currencyID)}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${this.formatCurrency(item.extendedPrice || 0, order.currencyID)}</td>
                  </tr>
                `).join('') : `
                  <tr>
                    <td colspan="6" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
                      No line items found for this order
                    </td>
                  </tr>
                `}
              </tbody>
              <tfoot class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th colspan="5" class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400">Total:</th>
                  <th class="px-3 py-2 text-right text-xs font-medium text-gray-800 dark:text-gray-200">${order.formattedTotal}</th>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>

        <!-- Buttons -->
        <div class="flex justify-end gap-2 mt-6">
          <button id="order-details-close" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
            Close
          </button>
          <button id="order-details-print" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
            Print Order
          </button>
        </div>
        </div>
      </div>
    `;
    
    // Create modal - matching opportunity.js structure exactly
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
    modalOverlay.id = 'order-details-modal';
    
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-4xl h-auto max-h-[80vh]';
    modalContainer.style.cssText = 'display: flex; flex-direction: column;';
    modalContainer.innerHTML = modalContent;
    
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);
    
    // Define closeModal function with better error handling
    const closeModal = () => {
      try {
        const modal = document.getElementById('order-details-modal');
        if (modal && modal.parentNode) {
          modal.parentNode.removeChild(modal);
        }
      } catch (error) {
        console.error('Error closing modal:', error);
      }
    };
    
    // Setup event listeners with better error handling
    try {
      const closeButton = document.getElementById('order-details-close');
      const printButton = document.getElementById('order-details-print');
      
      if (closeButton) {
        closeButton.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log('Close button clicked');
          closeModal();
        });
      } else {
        console.error('Close button not found');
      }
      
      if (printButton) {
        printButton.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log('Print button clicked');
          closeModal();
          this.printOrder(orderId);
        });
      } else {
        console.error('Print button not found');
      }
      
      // Add click outside to close
      modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
          closeModal();
        }
      });
      
      // Add keyboard event for Escape key - matching opportunity.js exactly
      const escapeHandler = (event) => {
        if (event.key === 'Escape') {
          closeModal();
          document.removeEventListener('keydown', escapeHandler);
        }
      };
      document.addEventListener('keydown', escapeHandler);
      
    } catch (error) {
      console.error('Error setting up modal event listeners:', error);
    }
  }

  printOrder(orderId) {
    const order = this.salesOrders.find(o => o.id === orderId);
    if (!order) {
      this.notificationSystem.addNotification("Order not found for printing", "error");
      return;
    }
    
    // Simple print implementation
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      this.notificationSystem.addNotification("Please allow pop-ups for this site to print orders", "error");
      return;
    }
    
    printWindow.document.write(`
      <html>
        <head>
          <title>Sales Order ${order.orderNbr}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { margin-bottom: 20px; }
            .details { margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Sales Order ${order.orderNbr}</h1>
            <p>Date: ${order.formattedDate}</p>
            <p>Status: ${order.status}</p>
          </div>
          
          <div class="details">
            <h3>Customer Information</h3>
            <p>Customer: ${order.customerName}</p>
            <p>Customer ID: ${order.customerID}</p>
          </div>
          
          <div class="details">
            <h3>Line Items</h3>
            <table>
              <thead>
                <tr>
                  <th>Line</th>
                  <th>Item #</th>
                  <th>Description</th>
                  <th>Qty</th>
                  <th>Unit Price</th>
                  <th>Extended</th>
                </tr>
              </thead>
              <tbody>
                ${order.details.map(item => `
                  <tr>
                    <td>${item.lineNbr}</td>
                    <td>${item.inventoryID}</td>
                    <td>${item.description}</td>
                    <td>${item.quantity} ${item.uom}</td>
                    <td>${this.formatCurrency(item.unitPrice, order.currencyID)}</td>
                    <td>${this.formatCurrency(item.extendedPrice, order.currencyID)}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
            
            <div style="margin-top: 20px; text-align: right;">
              <strong>Total: ${order.formattedTotal}</strong>
            </div>
          </div>
          
          <script>
            window.onload = function() {
              window.print();
              window.close();
            }
          </script>
        </body>
      </html>
    `);
    
    printWindow.document.close();
  }

  showError(message) {
    const errorElement = document.createElement('div');
    errorElement.id = 'sales-order-error-message';
    errorElement.className = 'mt-4 p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-md';
    errorElement.innerHTML = `
      <div class="flex items-center">
        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span>${message}</span>
      </div>
      <button class="mt-2 px-2 py-1 bg-red-200 dark:bg-red-800 rounded text-sm" id="dismiss-error">Dismiss</button>
    `;
    
    if (this.container) {
      this.container.appendChild(errorElement);
      
      // Add event listener to dismiss button
      const dismissButton = document.getElementById('dismiss-error');
      if (dismissButton) {
        dismissButton.addEventListener('click', () => {
          const errorMsg = document.getElementById('sales-order-error-message');
          if (errorMsg) {
            errorMsg.remove();
          }
        });
      }
    } else {
      console.error("Container not available to show error:", message);
      alert("Error: " + message);
    }
  }
} 