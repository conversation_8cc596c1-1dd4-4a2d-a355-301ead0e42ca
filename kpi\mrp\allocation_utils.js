// Shared Allocation Utilities for MRP Dashboard
// Centralized logic for component breakdown and inventory allocation

export class AllocationUtils {
  constructor() {
    this.productionOrders = [];
    this.inventoryItems = [];
  }

  // Initialize with production and inventory data
  setData(productionOrders, inventoryItems) {
    this.productionOrders = productionOrders || [];
    this.inventoryItems = inventoryItems || [];
  }

  // Main function to get full component breakdown for allocation
  getFullComponentBreakdown(salesOrders) {
    console.log("🔨 Building full component breakdown for allocation");
    
    const componentMap = new Map(); // Key: InventoryID, Value: component details
    
    // Process each sales order
    salesOrders.forEach((salesOrder) => {
      console.log(`📋 Processing sales order: ${salesOrder.OrderNbr}`);
      
      if (Array.isArray(salesOrder.LineItems)) {
        salesOrder.LineItems.forEach((lineItem) => {
          // Get components from this line item (either direct or via BOM)
          const lineComponents = this.getLineItemComponents(salesOrder, lineItem);
          
          // Merge into the global component map
          lineComponents.forEach(component => {
            const key = component.InventoryID;
            
            if (componentMap.has(key)) {
              // Add to existing component
              const existing = componentMap.get(key);
              existing.QtyRequired += component.QtyRequired;
              existing.Orders.push({
                OrderNbr: component.OrderNbr,
                QtyRequired: component.QtyRequired,
                ShippingDate: component.ShippingDate
              });
            } else {
              // Create new component entry
              componentMap.set(key, {
                ...component,
                Orders: [{
                  OrderNbr: component.OrderNbr,
                  QtyRequired: component.QtyRequired,
                  ShippingDate: component.ShippingDate
                }]
              });
            }
          });
        });
      }
    });
    
    // Convert map to array and add inventory info
    const componentBreakdown = Array.from(componentMap.values()).map(component => {
      const inventoryInfo = this.getInventoryInfo(component.InventoryID);
      
      return {
        ...component,
        LastCost: inventoryInfo.lastCost,
        QtyOnHand: inventoryInfo.qtyOnHand,
        UOM: inventoryInfo.uom || component.UOM,
        Found: inventoryInfo.found
      };
    });
    
    console.log(`📊 Built component breakdown with ${componentBreakdown.length} unique components`);
    return componentBreakdown;
  }

  // Get components for a specific line item (direct or via BOM)
  getLineItemComponents(salesOrder, lineItem) {
    const components = [];
    
    // Check if this line item has production orders
    const hasProduction = lineItem.BOMItems && lineItem.BOMItems.some(bom => 
      bom.ProductionNbr && bom.ProductionNbr.trim()
    );
    
    if (hasProduction) {
      // Get materials from production orders
      const processedProductionNbrs = new Set();
      
      lineItem.BOMItems.forEach(bomItem => {
        if (bomItem.ProductionNbr && bomItem.ProductionNbr.trim() && 
            !processedProductionNbrs.has(bomItem.ProductionNbr.trim())) {
          processedProductionNbrs.add(bomItem.ProductionNbr.trim());
          
          // Find the production order
          const productionOrder = this.productionOrders.find(po => 
            po.MainProductionNbr === bomItem.ProductionNbr.trim()
          );
          
          if (productionOrder && Array.isArray(productionOrder.Materials)) {
            productionOrder.Materials.forEach(material => {
              const inventoryId = material.InventoryID;
              const quantity = material.QtyRequired || 0;
              
              if (inventoryId && inventoryId.trim() && quantity > 0) {
                components.push({
                  OrderNbr: salesOrder.OrderNbr,
                  InventoryID: inventoryId.trim(),
                  Description: material.Description || '',
                  QtyRequired: quantity,
                  UOM: material.UOM || '',
                  ShippingDate: salesOrder.ShippingDate,
                  AllocationType: 'Production',
                  ProductionNbr: bomItem.ProductionNbr.trim(),
                  OperationNbr: material.OperationNbr || ''
                });
              }
            });
          }
        }
      });
    } else {
      // No production - use the line item itself
      const inventoryId = lineItem.InventoryID;
      const quantity = lineItem.Quantity || 0;
      
      if (inventoryId && inventoryId.trim() && quantity > 0) {
        components.push({
          OrderNbr: salesOrder.OrderNbr,
          InventoryID: inventoryId.trim(),
          Description: lineItem.LineDescription || '',
          QtyRequired: quantity,
          UOM: lineItem.UOM || '',
          ShippingDate: salesOrder.ShippingDate,
          AllocationType: 'Direct',
          ProductionNbr: '',
          OperationNbr: ''
        });
      }
    }
    
    return components;
  }

  // Get inventory information for a component
  getInventoryInfo(inventoryId) {
    if (!inventoryId || !this.inventoryItems.length) {
      return {
        lastCost: 0,
        qtyOnHand: 0,
        uom: '',
        found: false
      };
    }

    const inventoryItem = this.inventoryItems.find(item => 
      item.InventoryID === inventoryId.trim()
    );

    if (inventoryItem) {
      return {
        lastCost: inventoryItem.LastCost || 0,
        qtyOnHand: inventoryItem.QtyOnHand || 0,
        uom: inventoryItem.UOM || '',
        found: true
      };
    }

    return {
      lastCost: 0,
      qtyOnHand: 0,
      uom: '',
      found: false
    };
  }



  // Global allocation engine - the main function to use
  allocateComponentsGlobally(allSalesOrders, inventoryItems) {
    console.log("🌍 Starting global component allocation");
    console.log(`📋 Processing ${allSalesOrders.length} sales orders`);
    console.log(`📦 Using ${inventoryItems.length} inventory items`);

    // Step 1: Build global inventory pool
    const inventoryPool = {};
    inventoryItems.forEach(item => {
      inventoryPool[item.InventoryID] = {
        InventoryID: item.InventoryID,
        Description: item.Description || '',
        QtyOnHand: item.QtyOnHand || 0,
        QtyAllocated: 0,
        QtyRemaining: item.QtyOnHand || 0,
        LastCost: item.LastCost || 0,
        UOM: item.UOM || '',
        ReorderPoint: item.ReorderPoint || 0
      };
    });

    console.log(`🏊 Created inventory pool with ${Object.keys(inventoryPool).length} items`);

    // Step 2: Sort sales orders by shipping date (ascending - earliest first)
    const sortedOrders = [...allSalesOrders].sort((a, b) => {
      const dateA = this.parseShippingDate(a.ShippingDate);
      const dateB = this.parseShippingDate(b.ShippingDate);
      
      // Handle null dates - put them at the end
      if (!dateA && !dateB) return 0;
      if (!dateA) return 1;
      if (!dateB) return -1;
      
      return dateA.getTime() - dateB.getTime();
    });

    console.log(`📅 Sorted orders by shipping date`);

    // Step 3: Process each order in priority sequence
    const allocationResults = [];
    let globalSummary = {
      totalOrders: sortedOrders.length,
      totalComponents: 0,
      fullyAllocatedComponents: 0,
      partiallyAllocatedComponents: 0,
      shortageComponents: 0,
      totalShortageValue: 0
    };

    sortedOrders.forEach((salesOrder, orderIndex) => {
      console.log(`🔄 Processing order ${orderIndex + 1}/${sortedOrders.length}: ${salesOrder.OrderNbr}`);
      
      // Get component breakdown for this single order
      const orderComponents = this.getFullComponentBreakdown([salesOrder]);
      
      orderComponents.forEach(component => {
        // Process each component requirement for this order
        component.Orders.forEach(orderReq => {
          const inventoryId = component.InventoryID;
          const qtyRequired = orderReq.QtyRequired;
          
          // Get current inventory status
          const poolItem = inventoryPool[inventoryId];
          
          if (!poolItem) {
            // Item not found in inventory
            allocationResults.push({
              OrderNbr: orderReq.OrderNbr,
              InventoryID: inventoryId,
              Description: component.Description || 'Item not found',
              QtyRequired: qtyRequired,
              QtyOnHand: 0,
              QtyAllocated: 0,
              QtyShort: qtyRequired,
              QtyRemaining: 0,
              LastCost: 0,
              UOM: component.UOM || '',
              ShippingDate: orderReq.ShippingDate,
              AllocationType: component.AllocationType,
              AllocationStatus: 'Item Not Found',
              Priority: this.calculateAllocationPriority(orderReq),
              PriorityLabel: this.getPriorityLabel(this.calculateAllocationPriority(orderReq))
            });
            globalSummary.shortageComponents++;
            return;
          }

          // Calculate allocation
          const canAllocateQty = Math.min(qtyRequired, poolItem.QtyRemaining);
          const shortageQty = Math.max(0, qtyRequired - canAllocateQty);

          // Update global inventory pool
          poolItem.QtyAllocated += canAllocateQty;
          poolItem.QtyRemaining -= canAllocateQty;

          // Determine allocation status
          let allocationStatus;
          if (canAllocateQty >= qtyRequired) {
            allocationStatus = 'Fully Allocated';
            globalSummary.fullyAllocatedComponents++;
          } else if (canAllocateQty > 0) {
            allocationStatus = 'Partially Allocated';
            globalSummary.partiallyAllocatedComponents++;
          } else {
            allocationStatus = 'Insufficient Stock';
            globalSummary.shortageComponents++;
          }

          // Add to allocation results
          allocationResults.push({
            OrderNbr: orderReq.OrderNbr,
            InventoryID: inventoryId,
            Description: component.Description,
            QtyRequired: qtyRequired,
            QtyOnHand: poolItem.QtyOnHand,
            QtyAllocated: canAllocateQty,
            QtyShort: shortageQty,
            QtyRemaining: poolItem.QtyRemaining,
            LastCost: poolItem.LastCost,
            UOM: component.UOM || poolItem.UOM,
            ShippingDate: orderReq.ShippingDate,
            AllocationType: component.AllocationType,
            ProductionNbr: component.ProductionNbr || '',
            OperationNbr: component.OperationNbr || '',
            AllocationStatus: allocationStatus,
            Priority: this.calculateAllocationPriority(orderReq),
            PriorityLabel: this.getPriorityLabel(this.calculateAllocationPriority(orderReq))
          });

          globalSummary.totalComponents++;
          if (shortageQty > 0) {
            globalSummary.totalShortageValue += shortageQty * poolItem.LastCost;
          }
        });
      });
    });

    console.log(`🎯 Global allocation completed:`);
    console.log(`   📊 Total components: ${globalSummary.totalComponents}`);
    console.log(`   ✅ Fully allocated: ${globalSummary.fullyAllocatedComponents}`);
    console.log(`   ⚠️ Partially allocated: ${globalSummary.partiallyAllocatedComponents}`);
    console.log(`   ❌ Shortages: ${globalSummary.shortageComponents}`);

    return {
      allocations: allocationResults,
      inventoryPool: inventoryPool,
      summary: globalSummary
    };
  }

  // Legacy method for backward compatibility - now calls the global allocation
  performSmartAllocation(salesOrders, inventoryItems, existingAllocations = []) {
    console.log("🔄 Legacy performSmartAllocation called - redirecting to global allocation");
    const result = this.allocateComponentsGlobally(salesOrders, inventoryItems);
    
    return {
      allocations: result.allocations,
      summary: {
        totalRequirements: result.summary.totalComponents,
        fullyAllocated: result.summary.fullyAllocatedComponents,
        partiallyAllocated: result.summary.partiallyAllocatedComponents,
        shortages: result.summary.shortageComponents
      }
    };
  }

  // Calculate allocation priority based on shipping date
  calculateAllocationPriority(requirement) {
    const shippingDate = this.parseShippingDate(requirement.ShippingDate);
    const now = new Date();
    
    if (!shippingDate) {
      return 7; // No shipping date - lowest priority
    }
    
    const daysUntilShipping = Math.ceil((shippingDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysUntilShipping < 0) {
      return 1; // Past due - highest priority
    } else if (daysUntilShipping <= 1) {
      return 2; // Ships tomorrow or today
    } else if (daysUntilShipping <= 3) {
      return 3; // Ships within 3 days
    } else if (daysUntilShipping <= 7) {
      return 4; // Ships within a week
    } else if (daysUntilShipping <= 14) {
      return 5; // Ships within 2 weeks
    } else {
      return 6; // Ships in more than 2 weeks
    }
  }

  // Get priority label
  getPriorityLabel(priority) {
    switch (priority) {
      case 1: return 'Past Due';
      case 2: return 'Urgent';
      case 3: return 'High';
      case 4: return 'Medium-High';
      case 5: return 'Medium';
      case 6: return 'Low';
      case 7: return 'No Date';
      default: return 'Unknown';
    }
  }

  // Parse shipping date from various formats
  parseShippingDate(shippingDate) {
    if (!shippingDate) return null;
    
    try {
      if (typeof shippingDate === 'object' && shippingDate.year) {
        // Acumatica date format: {year: 2024, month: 12, day: 15}
        return new Date(shippingDate.year, shippingDate.month - 1, shippingDate.day);
      } else if (typeof shippingDate === 'string') {
        return new Date(shippingDate);
      }
    } catch (error) {
      console.warn('Error parsing shipping date:', shippingDate, error);
    }
    
    return null;
  }

  // Get allocated quantity for an inventory item across all orders
  getTotalAllocatedForItem(inventoryId, allocationResults) {
    return allocationResults
      .filter(alloc => alloc.InventoryID === inventoryId)
      .reduce((sum, alloc) => sum + (alloc.QtyAllocated || 0), 0);
  }

  // Get allocation for specific item and order combination
  getAllocationForItemOrder(inventoryId, orderNbr, allocationResults) {
    return allocationResults.find(alloc => 
      alloc.InventoryID === inventoryId && alloc.OrderNbr === orderNbr
    );
  }
} 