// part-image-helper.js - Utility functions for handling part images from Acumatica
import { connectionManager } from "../core/connection.js";

export class PartImageHelper {
  constructor() {
    this.imageCache = new Map(); // Cache images to avoid redundant API calls
    this.pendingRequests = new Map(); // Track ongoing requests
    this.noImageUrl = chrome.runtime.getURL('images/extra/no-thumbnail.png'); // Fallback image
    this.customPartsDB = null; // Reference to custom parts database
  }

  /**
   * Set reference to custom parts database
   * @param {Object} customPartsDB - Custom parts database instance
   */
  setCustomPartsDB(customPartsDB) {
    this.customPartsDB = customPartsDB;
  }

  /**
   * Check if a part is a custom part by checking with the master parts component
   * @param {string} partId - The inventory/part ID
   * @returns {boolean} - True if it's a custom part
   */
  isCustomPart(partId, masterPartsComponent = null) {
    if (masterPartsComponent) {
      const part = masterPartsComponent.partsData.find(p => p['Part Number'] === partId);
      return part && part._isCustomPart;
    }
    return false;
  }

  /**
   * Fetch image for a single part (handles both custom and Acumatica parts)
   * @param {string} partId - The inventory/part ID
   * @param {Object} masterPartsComponent - Reference to master parts component (optional)
   * @returns {Promise<{success: boolean, imageUrl: string, error?: string, fileName?: string}>}
   */
  async getPartImage(partId, masterPartsComponent = null) {
    // Check if image is already in cache
    if (this.imageCache.has(partId)) {
      return {
        success: true,
        imageUrl: this.imageCache.get(partId)
      };
    }

    // Check if there's already a pending request for this partId
    if (this.pendingRequests.has(partId)) {
      // Return the existing promise to avoid duplicate requests
      return this.pendingRequests.get(partId);
    }

    // Check if this is a custom part
    if (this.customPartsDB && this.isCustomPart(partId, masterPartsComponent)) {
      const requestPromise = this.getCustomPartImage(partId);
      this.pendingRequests.set(partId, requestPromise);
      return requestPromise;
    }

    // Handle Acumatica parts
    return this.getAcumaticaPartImage(partId);
  }

  /**
   * Get custom part image from IndexedDB
   * @param {string} partId - The inventory/part ID
   * @returns {Promise<{success: boolean, imageUrl: string, error?: string}>}
   */
  async getCustomPartImage(partId) {
    return new Promise(async (resolve) => {
      try {
        const imageData = await this.customPartsDB.getPartImage(partId);
        if (imageData) {
          this.imageCache.set(partId, imageData);
          resolve({
            success: true,
            imageUrl: imageData,
            fileName: `Custom_${partId}`
          });
        } else {
          this.imageCache.set(partId, this.noImageUrl);
          resolve({
            success: false,
            imageUrl: this.noImageUrl,
            error: "No custom image available"
          });
        }
      } catch (error) {
        console.error(`Error loading custom part image for ${partId}:`, error);
        this.imageCache.set(partId, this.noImageUrl);
        resolve({
          success: false,
          imageUrl: this.noImageUrl,
          error: error.message || "Failed to load custom image"
        });
      } finally {
        this.pendingRequests.delete(partId);
      }
    });
  }

  /**
   * Get Acumatica part image (original functionality)
   * @param {string} partId - The inventory/part ID
   * @returns {Promise<{success: boolean, imageUrl: string, error?: string, fileName?: string}>}
   */
  async getAcumaticaPartImage(partId) {
    // Check if connected to Acumatica
    const connectionStatus = connectionManager.getConnectionStatus();
    if (!connectionStatus.acumatica.isConnected) {
      return {
        success: false,
        imageUrl: this.noImageUrl,
        error: "Not connected to Acumatica"
      };
    }

    // Create a new promise for this request
    const requestPromise = new Promise(async (resolve) => {
      try {
        const instance = connectionManager.connections.acumatica.instance;

        // Step 1: Get file information from Acumatica
        console.log(`Fetching image info for part ${partId}...`);
        const fileInfoResponse = await this.fetchPartImageInfo(partId);
        
        if (!fileInfoResponse.success || !fileInfoResponse.data) {
          this.imageCache.set(partId, this.noImageUrl); // Cache no-image result
          resolve({
            success: false,
            imageUrl: this.noImageUrl,
            error: fileInfoResponse.error || "No image data found"
          });
          return;
        }

        // Step 2: Extract file ID from response
        const fileId = this.extractFileId(fileInfoResponse.data);
        if (!fileId) {
          this.imageCache.set(partId, this.noImageUrl);
          resolve({
            success: false,
            imageUrl: this.noImageUrl,
            error: "No images available for this part"
          });
          return;
        }

        // Step 3: Build the image URL
        const fileName = fileInfoResponse.fileName || `Part_${partId}`;
        const imageUrl = `${instance}/entity/Default/20.200.001/files/${fileId}`;
        
        // Cache the result
        this.imageCache.set(partId, imageUrl);
        
        resolve({
          success: true,
          imageUrl: imageUrl,
          fileName: fileName
        });
      } catch (error) {
        console.error(`Error fetching image for part ${partId}:`, error);
        this.imageCache.set(partId, this.noImageUrl); // Cache the fallback on error
        resolve({
          success: false,
          imageUrl: this.noImageUrl,
          error: error.message || "Failed to fetch image"
        });
      } finally {
        // Remove from pending requests
        this.pendingRequests.delete(partId);
      }
    });

    // Store the promise for this partId
    this.pendingRequests.set(partId, requestPromise);
    
    // Return the promise
    return requestPromise;
  }

  /**
   * Extract file ID from Acumatica API response
   * @param {Object} data - Response data from Acumatica API
   * @returns {string|null} - File ID or null if not found
   */
  extractFileId(data) {
    try {
      // Handle different response formats
      if (data.value && Array.isArray(data.value) && data.value.length > 0) {
        // Format 1: { value: [ { files: { value: [ { id: "xxx" } ] } } ] }
        const stockItem = data.value[0];
        if (stockItem.files && stockItem.files.value && stockItem.files.value.length > 0) {
          return stockItem.files.value[0].id;
        }
      } else if (data.length > 0) {
        // Format 2: [ { files: [ { id: "xxx" } ] } ]
        if (data[0].files && data[0].files.length > 0) {
          return data[0].files[0].id;
        }
      }
      return null;
    } catch (e) {
      console.error("Error extracting file ID:", e);
      return null;
    }
  }

  /**
   * Fetch part image information from Acumatica
   * @param {string} partId - The inventory/part ID
   * @returns {Promise<{success: boolean, data?: Object, error?: string, fileName?: string}>}
   */
  async fetchPartImageInfo(partId) {
    try {
      // Use the connection manager to get the image info
      const fileInfoResponse = await connectionManager.getStockItemImage(partId);
      return fileInfoResponse;
    } catch (error) {
      console.error("Error in fetchPartImageInfo:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Validate if a URL returns a valid image
   * @param {string} url - Image URL to validate
   * @returns {Promise<boolean>} - True if valid image
   */
  async validateImageUrl(url) {
    try {
      // Get cookies for authentication
      const instance = connectionManager.connections.acumatica.instance;
      let cookieString = '';
      
      if (chrome?.cookies?.getAll) {
        try {
          const urlObj = new URL(instance);
          const cookies = await chrome.cookies.getAll({ domain: urlObj.hostname });
          cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
        } catch (error) {
          // Silently continue without cookies
        }
      }

      // Try to fetch the image
      const headers = {
        'Accept': 'image/*'
      };
      
      if (cookieString) {
        headers['Cookie'] = cookieString;
      }
      
      const response = await fetch(url, {
        method: 'HEAD',
        headers: headers
      });
      
      return response.ok && 
        response.headers.get('content-type') && 
        response.headers.get('content-type').startsWith('image/');
    } catch (error) {
      console.error("Error validating image URL:", error);
      return false;
    }
  }

  /**
   * Fetch images for multiple parts
   * @param {string[]} partIds - Array of inventory/part IDs
   * @returns {Promise<Map<string, {success: boolean, imageUrl: string, error?: string}>>}
   */
  async getMultiplePartImages(partIds) {
    // Check if connected to Acumatica
    const connectionStatus = connectionManager.getConnectionStatus();
    if (!connectionStatus.acumatica.isConnected) {
      const resultMap = new Map();
      partIds.forEach(partId => {
        resultMap.set(partId, {
          success: false,
          imageUrl: this.noImageUrl,
          error: "Not connected to Acumatica"
        });
      });
      return resultMap;
    }

    // Filter out parts that are already in cache
    const uncachedPartIds = partIds.filter(partId => !this.imageCache.has(partId));
    
    // Create a result map with the cached results
    const resultMap = new Map();
    partIds.filter(partId => this.imageCache.has(partId)).forEach(partId => {
      resultMap.set(partId, {
        success: true,
        imageUrl: this.imageCache.get(partId)
      });
    });

    // If all parts are already cached, return early
    if (uncachedPartIds.length === 0) {
      return resultMap;
    }

    // Process each part individually - safer approach
    const batchSize = 5; // Process in smaller batches to avoid overwhelming the server
    
    for (let i = 0; i < uncachedPartIds.length; i += batchSize) {
      const batch = uncachedPartIds.slice(i, i + batchSize);
      
      // Process batch in parallel
      await Promise.all(batch.map(async (partId) => {
        try {
          const result = await this.getPartImage(partId);
          resultMap.set(partId, result);
        } catch (error) {
          console.error(`Error fetching image for part ${partId}:`, error);
          this.imageCache.set(partId, this.noImageUrl);
          resultMap.set(partId, {
            success: false,
            imageUrl: this.noImageUrl,
            error: error.message || "Failed to fetch image"
          });
        }
      }));
      
      // Small delay between batches to be kind to the server
      if (i + batchSize < uncachedPartIds.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return resultMap;
  }

  /**
   * Creates a base64 data URL from an image URL (for embedding in exports)
   * @param {string} imageUrl - The URL of the image
   * @returns {Promise<string>} - Base64 data URL
   */
  async getImageAsDataUrl(imageUrl) {
    try {
      // Skip conversion for no-image URL (already embedded)
      if (imageUrl === this.noImageUrl) {
        return imageUrl;
      }

      // Get connection status for cookies
      const connectionStatus = connectionManager.getConnectionStatus();
      if (!connectionStatus.acumatica.isConnected) {
        return this.noImageUrl;
      }

      // Get cookies for authentication
      let cookieString = '';
      const instance = connectionManager.connections.acumatica.instance;
      
      if (chrome?.cookies?.getAll) {
        try {
          const url = new URL(instance);
          const cookies = await chrome.cookies.getAll({ domain: url.hostname });
          cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
        } catch (error) {
          // Silently continue without cookies
        }
      }

      // Fetch the image with cookies (if available)
      const headers = {
        'Accept': 'image/*'
      };
      
      if (cookieString) {
        headers['Cookie'] = cookieString;
      }
      
      const response = await fetch(imageUrl, {
        method: 'GET',
        headers: headers
      });

      if (!response.ok) {
        console.warn(`Failed to fetch image: ${response.status}`);
        return this.noImageUrl;
      }

      // Convert to blob and then to base64
      const blob = await response.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error("Error converting image to data URL:", error);
      return this.noImageUrl;
    }
  }
  
  /**
   * Clear the image cache
   * Use this when logging out or changing Acumatica connection
   */
  clearCache() {
    this.imageCache.clear();
  }
}

// Create a singleton instance
export const partImageHelper = new PartImageHelper();
