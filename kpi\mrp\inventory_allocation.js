// Inventory Allocation component for MRP Dashboard
import { AllocationUtils } from './allocation_utils.js';

export class InventoryAllocationComponent {
  constructor(container, parentComponent) {
    console.log("🏗️ InventoryAllocationComponent Constructor");
    
    this.container = container;
    this.parentComponent = parentComponent;
    this.allocations = [];
    this.filteredAllocations = [];
    this.currentPage = 1;
    this.itemsPerPage = 50;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'OrderNbr';
    this.sortDirection = 'asc';
    this.isLoading = false;
    
    // Flag to determine if this component should render only content (when used as tab)
    this.containerOnly = false;

    // Database configuration for allocations
    this.dbName = 'inventoryDb';
    this.allocationStoreName = 'inventoryAllocations';
    this.salesOrderDbName = 'salesOrderDb';
    this.salesOrderStoreName = 'salesOrders';
    this.productionDbName = 'productionDb';
    this.productionStoreName = 'productionOrders';
    
    // Cache for data
    this.salesOrders = [];
    this.productionOrders = [];
    this.inventoryItems = [];
    
    // Global inventory pool and allocation engine
    this.allocationUtils = new AllocationUtils();
    this.inventoryPool = new Map();
    this.allocationResults = [];
    this.allocationSummary = {};
    
    console.log("🏗️ InventoryAllocationComponent Constructor Complete");
  }

  async init() {
    console.log("=== INVENTORY ALLOCATION INIT STARTED ===");
    
    await this.refresh();
  }

  async refresh() {
    console.log("=== REFRESHING INVENTORY ALLOCATION ===");

    this.isLoading = true;
    this.render();

    try {
      console.log("Loading all required data for smart allocation...");
      // Load all required data in parallel
      await Promise.all([
        this.loadSalesOrders(),
        this.loadProductionOrders(),
        this.loadInventoryItems(),
        this.loadExistingAllocations()
      ]);
      
      console.log("Initializing allocation utils with data...");
      // Initialize allocation utilities with loaded data
      this.allocationUtils.setData(this.productionOrders, this.inventoryItems);
      
      console.log("Performing global component allocation...");
      // Use the new global allocation method
      const result = this.allocationUtils.allocateComponentsGlobally(
        this.salesOrders, 
        this.inventoryItems
      );
      
      this.allocationResults = result.allocations;
      this.inventoryPool = result.inventoryPool;
      this.allocationSummary = result.summary;
      
      console.log("Applying filters and building display...");
      // Apply filters and build filtered view
      this.filteredAllocations = [...this.allocationResults];
      this.calculateTotalPages();
      this.applyFilters();

      this.isLoading = false;
      this.render();
      console.log("=== INVENTORY ALLOCATION REFRESH COMPLETED ===");
      console.log(`📊 Allocation Summary:`, this.allocationSummary);
    } catch (error) {
      console.error("=== ERROR IN INVENTORY ALLOCATION REFRESH ===");
      console.error("Error refreshing allocations:", error);
      this.isLoading = false;
      this.showError("Failed to refresh allocations: " + error.message);
      this.render();
    }
  }

  async loadSalesOrders() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.salesOrderDbName);
      
      request.onerror = (event) => {
        console.warn("Could not open sales order database:", event.target.error);
        this.salesOrders = [];
        resolve(); // Continue without sales order data
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        
        if (!db.objectStoreNames.contains(this.salesOrderStoreName)) {
          console.warn("Sales order store not found, continuing without sales data");
          this.salesOrders = [];
          db.close();
          resolve();
          return;
        }
        
        const transaction = db.transaction([this.salesOrderStoreName], "readonly");
        const store = transaction.objectStore(this.salesOrderStoreName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          this.salesOrders = getAllRequest.result;
          console.log(`Loaded ${this.salesOrders.length} sales orders for allocation analysis`);
          resolve();
        };
        
        getAllRequest.onerror = (event) => {
          console.warn("Error loading sales orders:", event.target.error);
          this.salesOrders = [];
          resolve(); // Continue without sales order data
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
      };
    });
  }

  async loadProductionOrders() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.productionDbName);
      
      request.onerror = (event) => {
        console.warn("Could not open production database:", event.target.error);
        this.productionOrders = [];
        resolve(); // Continue without production data
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        
        if (!db.objectStoreNames.contains(this.productionStoreName)) {
          console.warn("Production store not found, continuing without production data");
          this.productionOrders = [];
          db.close();
          resolve();
          return;
        }
        
        const transaction = db.transaction([this.productionStoreName], "readonly");
        const store = transaction.objectStore(this.productionStoreName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          this.productionOrders = getAllRequest.result;
          console.log(`Loaded ${this.productionOrders.length} production orders for allocation`);
          resolve();
        };
        
        getAllRequest.onerror = (event) => {
          console.warn("Error loading production orders:", event.target.error);
          this.productionOrders = [];
          resolve(); // Continue without production data
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
      };
    });
  }

  async loadInventoryItems() {
    return new Promise((resolve, reject) => {
      // Get inventory items from parent component if available
      if (this.parentComponent && this.parentComponent.inventoryItems) {
        this.inventoryItems = this.parentComponent.inventoryItems;
        console.log(`Loaded ${this.inventoryItems.length} inventory items from parent component`);
        resolve();
        return;
      }
      
      // Otherwise load from IndexedDB
      const request = indexedDB.open(this.dbName);
      
      request.onerror = (event) => {
        console.warn("Could not open inventory database:", event.target.error);
        this.inventoryItems = [];
        resolve(); // Continue without inventory data
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        
        if (!db.objectStoreNames.contains('inventoryItems')) {
          console.warn("Inventory store not found, continuing without inventory data");
          this.inventoryItems = [];
          db.close();
          resolve();
          return;
        }
        
        const transaction = db.transaction(['inventoryItems'], "readonly");
        const store = transaction.objectStore('inventoryItems');
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          this.inventoryItems = getAllRequest.result;
          console.log(`Loaded ${this.inventoryItems.length} inventory items from database`);
          resolve();
        };
        
        getAllRequest.onerror = (event) => {
          console.warn("Error loading inventory items:", event.target.error);
          this.inventoryItems = [];
          resolve(); // Continue without inventory data
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
      };
    });
  }

  async loadExistingAllocations() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName);
      
      request.onerror = (event) => {
        console.warn("Could not open allocation database:", event.target.error);
        this.allocations = [];
        resolve(); // Continue without allocation data
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        
        // Check if allocation store exists, if not create it
        if (!db.objectStoreNames.contains(this.allocationStoreName)) {
          // Close this connection and reopen with version upgrade
          db.close();
          this.createAllocationStore().then(() => {
            this.allocations = [];
            resolve();
          }).catch(() => {
            this.allocations = [];
            resolve();
          });
          return;
        }
        
        const transaction = db.transaction([this.allocationStoreName], "readonly");
        const store = transaction.objectStore(this.allocationStoreName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          this.allocations = getAllRequest.result || [];
          console.log(`Loaded ${this.allocations.length} existing allocations`);
          resolve();
        };
        
        getAllRequest.onerror = (event) => {
          console.warn("Error loading allocations:", event.target.error);
          this.allocations = [];
          resolve(); // Continue without allocation data
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
      };
    });
  }

  async createAllocationStore() {
    return new Promise((resolve, reject) => {
      // First, get current version
      const checkRequest = indexedDB.open(this.dbName);
      
      checkRequest.onsuccess = (event) => {
        const db = event.target.result;
        const currentVersion = db.version;
        db.close();
        
        // Now open with upgraded version
        const upgradeRequest = indexedDB.open(this.dbName, currentVersion + 1);
        
        upgradeRequest.onupgradeneeded = (event) => {
          const db = event.target.result;
          
          // Create allocation store if it doesn't exist
          if (!db.objectStoreNames.contains(this.allocationStoreName)) {
            const store = db.createObjectStore(this.allocationStoreName, { keyPath: "id" });
            
            // Create indices for allocation queries
            store.createIndex("InventoryID", "InventoryID", { unique: false });
            store.createIndex("OrderNbr", "OrderNbr", { unique: false });
            store.createIndex("AllocationType", "AllocationType", { unique: false });
            store.createIndex("CreatedDate", "CreatedDate", { unique: false });
            
            console.log("Created inventory allocation store");
          }
        };
        
        upgradeRequest.onsuccess = () => {
          console.log("Allocation store created successfully");
          resolve();
        };
        
        upgradeRequest.onerror = (event) => {
          console.error("Error creating allocation store:", event.target.error);
          reject(event.target.error);
        };
      };
      
      checkRequest.onerror = (event) => {
        console.error("Error checking database version:", event.target.error);
        reject(event.target.error);
      };
    });
  }

  // Method to get allocated quantity for an inventory item (used by parent component)
  getAllocatedQuantity(inventoryId) {
    return this.allocationUtils.getTotalAllocatedForItem(inventoryId, this.allocationResults);
  }

  // Format shipping date for display (wrapper around utility function)
  formatShippingDateForDisplay(shippingDate) {
    const date = this.allocationUtils.parseShippingDate(shippingDate);
    if (!date) return 'No Date';
    
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    const dateStr = date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
    
    if (diffDays < 0) {
      return `${dateStr} (${Math.abs(diffDays)}d ago)`;
    } else if (diffDays === 0) {
      return `${dateStr} (Today)`;
    } else if (diffDays === 1) {
      return `${dateStr} (Tomorrow)`;
    } else if (diffDays <= 7) {
      return `${dateStr} (${diffDays}d)`;
    } else {
      return dateStr;
    }
  }

  // Method to update search term from parent component
  updateSearchTerm(searchTerm) {
    this.searchTerm = searchTerm || '';
    this.currentPage = 1;
    this.applyFilters();
    if (!this.isLoading) {
      this.render();
    }
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredAllocations.length / this.itemsPerPage));

    // Adjust current page if it's out of bounds
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  applyFilters() {
    // Use allocation results as the base data for filtering
    let filtered = [...this.allocationResults];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(alloc => {
        return (
          (alloc.InventoryID || '').toLowerCase().includes(searchLower) ||
          (alloc.OrderNbr || '').toLowerCase().includes(searchLower) ||
          (alloc.Description || '').toLowerCase().includes(searchLower) ||
          (alloc.AllocationStatus || '').toLowerCase().includes(searchLower) ||
          (alloc.PriorityLabel || '').toLowerCase().includes(searchLower) ||
          (alloc.AllocationType || '').toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (this.sortField) {
        case 'InventoryID':
        case 'OrderNbr':
        case 'Description':
        case 'AllocationStatus':
        case 'PriorityLabel':
        case 'AllocationType':
          aValue = a[this.sortField] || '';
          bValue = b[this.sortField] || '';
          break;
        case 'QtyRequired':
        case 'QtyAllocated':
        case 'QtyShort':
        case 'QtyOnHand':
        case 'Priority':
          aValue = parseFloat(a[this.sortField]) || 0;
          bValue = parseFloat(b[this.sortField]) || 0;
          break;
        case 'ShippingDate':
          // Sort by actual date
          aValue = this.allocationUtils.parseShippingDate(a.ShippingDate);
          bValue = this.allocationUtils.parseShippingDate(b.ShippingDate);
          // Handle null dates - put them at the end
          if (!aValue && !bValue) return 0;
          if (!aValue) return 1;
          if (!bValue) return -1;
          aValue = aValue.getTime();
          bValue = bValue.getTime();
          break;
        default:
          aValue = a[this.sortField] || '';
          bValue = b[this.sortField] || '';
      }

      // Handle different data types
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return this.sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      } else {
        // String comparison
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        if (this.sortDirection === 'asc') {
          return aStr.localeCompare(bStr);
        } else {
          return bStr.localeCompare(aStr);
        }
      }
    });

    this.filteredAllocations = filtered;
    this.calculateTotalPages();
    this.currentPage = 1; // Reset to first page when filtering
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    const tableContainer = this.container;
    if (!tableContainer) return;

    tableContainer.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading allocation data...</p>
      </div>
    `;
  }

  renderContent() {
    const tableContainer = this.container;
    if (!tableContainer) return;

    console.log("Rendering allocation content:");
    console.log("- Total allocations:", this.allocations.length);
    console.log("- Filtered allocations:", this.filteredAllocations.length);
    console.log("- Sales orders:", this.salesOrders.length);
    console.log("- Container only mode:", this.containerOnly);

    // Calculate pagination
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = Math.min(startIndex + this.itemsPerPage, this.filteredAllocations.length);
    const pageAllocations = this.filteredAllocations.slice(startIndex, endIndex);

    // When used as a tab (containerOnly), render without the wrapper div
    const wrapperClass = this.containerOnly ? '' : 'bg-white dark:bg-gray-800 rounded-lg shadow';
    const content = `
      ${this.containerOnly ? '' : '<div class="bg-white dark:bg-gray-800 rounded-lg shadow">'}
        <!-- Controls Section -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex flex-col md:flex-row justify-between items-center gap-4">
            <div class="flex items-center gap-2">
              <button id="auto-allocate-button" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md flex items-center">
                <i class="fas fa-magic mr-2"></i>
                Auto Allocate All
              </button>
              <button id="clear-allocations-button" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md flex items-center">
                <i class="fas fa-trash mr-2"></i>
                Clear All Allocations
              </button>
            </div>
            
            <div class="text-sm text-gray-600 dark:text-gray-400">
              <span class="font-medium">${this.getTotalRequirements()}</span> total requirements •
              <span class="font-medium text-green-600">${this.getFullyAllocatedCount()}</span> fully allocated •
              <span class="font-medium text-red-600">${this.getShortageCount()}</span> shortages
            </div>
          </div>
        </div>

        <!-- Allocation Table -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="OrderNbr">
                  Order # <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="InventoryID">
                  Item # <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Description
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="QtyRequired">
                  Required <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="QtyOnHand">
                  On Hand <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Available
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="QtyAllocated">
                  Allocated <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="QtyShort">
                  Shortage <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Priority">
                  Priority <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="ShippingDate">
                  Ship Date <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="AllocationStatus">
                  Status <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${this.renderAllocationRows(pageAllocations)}
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="flex flex-col sm:flex-row justify-between items-center mt-4 p-4 space-y-3 sm:space-y-0">
          <div class="text-sm text-gray-500 dark:text-gray-400">
            Showing ${Math.min(startIndex + 1, this.filteredAllocations.length)} to 
            ${Math.min(endIndex, this.filteredAllocations.length)} of 
            ${this.filteredAllocations.length} results
          </div>
          
          <div class="flex items-center space-x-1">
            <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-double-left"></i>
            </button>
            <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-left"></i>
            </button>
            
            <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
              ${this.currentPage} of ${this.totalPages}
            </span>
            
            <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
              <i class="fas fa-angle-right"></i>
            </button>
            <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
              <i class="fas fa-angle-double-right"></i>
            </button>
          </div>
        </div>
      ${this.containerOnly ? '' : '</div>'}
    `;

    tableContainer.innerHTML = content;
    this.setupAllocationEventListeners();
  }

  renderAllocationRows(allocations) {
    if (!allocations || allocations.length === 0) {
      return `
        <tr>
          <td colspan="12" class="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
            <div class="flex flex-col items-center">
              <svg class="w-12 h-12 mb-4 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
              </svg>
              <p class="text-lg font-medium mb-2">No allocation requirements found</p>
              <p class="text-sm">Load sales orders first to view allocation requirements</p>
            </div>
          </td>
        </tr>
      `;
    }

    return allocations.map(allocation => {
      return `
        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
          <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
            ${this.escapeHtml(allocation.OrderNbr)}
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-blue-600 dark:text-blue-400">
            ${this.escapeHtml(allocation.InventoryID)}
          </td>
          <td class="px-4 py-3 text-sm text-gray-900 dark:text-white">
            <div class="max-w-xs truncate" title="${this.escapeHtml(allocation.Description)}">
              ${this.escapeHtml(this.truncateText(allocation.Description, 40))}
            </div>
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
            <span class="font-medium">${this.formatNumber(allocation.QtyRequired)}</span>
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
            ${this.formatNumber(allocation.QtyOnHand)}
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-right">
            <span class="font-medium ${allocation.QtyRemaining >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}">
              ${this.formatNumber(allocation.QtyRemaining || 0)}
            </span>
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-right">
            <span class="font-medium ${allocation.QtyAllocated > 0 ? 'text-orange-600 dark:text-orange-400' : 'text-gray-500 dark:text-gray-400'}">
              ${this.formatNumber(allocation.QtyAllocated)}
            </span>
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-right">
            <span class="font-medium ${allocation.QtyShort > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}">
              ${this.formatNumber(allocation.QtyShort)}
            </span>
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-center">
            <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${this.getPriorityClass(allocation.Priority)}">
              ${this.escapeHtml(allocation.PriorityLabel || 'Medium')}
            </span>
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-center text-gray-900 dark:text-white">
            <div class="text-xs ${this.getShippingDateClass(allocation.ShippingDate)}">
              ${this.escapeHtml(this.formatShippingDateForDisplay(allocation.ShippingDate))}
            </div>
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-center">
            <span class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${this.getStatusClass(allocation.AllocationStatus)}">
              ${this.escapeHtml(allocation.AllocationStatus)}
            </span>
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
            <div class="flex justify-end gap-1">
              ${allocation.AllocationStatus !== 'Fully Allocated' && allocation.QtyRemaining >= allocation.QtyRequired ? `
                <button class="allocate-button px-2 py-1 text-xs bg-green-100 hover:bg-green-200 text-green-800 rounded" 
                        data-inventory-id="${allocation.InventoryID}" 
                        data-order-nbr="${allocation.OrderNbr}"
                        data-required-qty="${allocation.QtyRequired}"
                        title="Allocate inventory">
                  <i class="fas fa-check"></i>
                </button>
              ` : ''}
              ${allocation.QtyAllocated > 0 ? `
                <button class="deallocate-button px-2 py-1 text-xs bg-red-100 hover:bg-red-200 text-red-800 rounded" 
                        data-inventory-id="${allocation.InventoryID}" 
                        data-order-nbr="${allocation.OrderNbr}"
                        title="Remove allocation">
                  <i class="fas fa-times"></i>
                </button>
              ` : ''}
              ${allocation.QtyShort > 0 ? `
                <button class="generate-demand-button px-2 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 rounded" 
                        data-inventory-id="${allocation.InventoryID}" 
                        data-shortage="${allocation.QtyShort}"
                        title="Generate purchase/production demand">
                  <i class="fas fa-plus"></i>
                </button>
              ` : ''}
            </div>
          </td>
        </tr>
      `;
    }).join('');
  }

  setupAllocationEventListeners() {
    // Auto allocate button
    const autoAllocateButton = document.getElementById('auto-allocate-button');
    if (autoAllocateButton) {
      autoAllocateButton.addEventListener('click', () => {
        this.autoAllocateAll();
      });
    }

    // Clear allocations button
    const clearAllocationsButton = document.getElementById('clear-allocations-button');
    if (clearAllocationsButton) {
      clearAllocationsButton.addEventListener('click', () => {
        this.clearAllAllocations();
      });
    }

    // Individual allocation buttons
    const allocateButtons = document.querySelectorAll('.allocate-button');
    allocateButtons.forEach(button => {
      button.addEventListener('click', (event) => {
        event.preventDefault();
        event.stopPropagation();
        
        const inventoryId = button.getAttribute('data-inventory-id');
        const orderNbr = button.getAttribute('data-order-nbr');
        const requiredQty = parseFloat(button.getAttribute('data-required-qty'));
        
        this.allocateInventory(inventoryId, orderNbr, requiredQty);
      });
    });

    // Individual deallocation buttons
    const deallocateButtons = document.querySelectorAll('.deallocate-button');
    deallocateButtons.forEach(button => {
      button.addEventListener('click', (event) => {
        event.preventDefault();
        event.stopPropagation();
        
        const inventoryId = button.getAttribute('data-inventory-id');
        const orderNbr = button.getAttribute('data-order-nbr');
        
        this.deallocateInventory(inventoryId, orderNbr);
      });
    });

    // Generate demand buttons
    const generateDemandButtons = document.querySelectorAll('.generate-demand-button');
    generateDemandButtons.forEach(button => {
      button.addEventListener('click', (event) => {
        event.preventDefault();
        event.stopPropagation();
        
        const inventoryId = button.getAttribute('data-inventory-id');
        const shortage = parseFloat(button.getAttribute('data-shortage'));
        
        this.generateDemand(inventoryId, shortage);
      });
    });

    // Pagination buttons
    this.setupPaginationListeners();

    // Sort headers
    const sortHeaders = document.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'asc';
        }
        this.applyFilters();
        this.render();
      });
    });
  }

  setupPaginationListeners() {
    // Pagination event handlers
    const firstPageBtn = document.getElementById('first-page');
    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage = 1;
          this.render();
        }
      });
    }

    const prevPageBtn = document.getElementById('prev-page');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.render();
        }
      });
    }

    const nextPageBtn = document.getElementById('next-page');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.render();
        }
      });
    }

    const lastPageBtn = document.getElementById('last-page');
    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage = this.totalPages;
          this.render();
        }
      });
    }
  }

  async allocateInventory(inventoryId, orderNbr, requiredQty) {
    try {
      console.log(`Allocating ${requiredQty} of ${inventoryId} to order ${orderNbr}`);
      
      // Create or update allocation
      const allocation = {
        id: `alloc-${inventoryId}-${orderNbr}`,
        InventoryID: inventoryId,
        OrderNbr: orderNbr,
        AllocatedQty: requiredQty,
        RequiredQty: requiredQty,
        AllocationType: 'Manual',
        CreatedDate: new Date().toISOString(),
        Status: 'Active'
      };
      
      await this.saveAllocation(allocation);
      await this.refresh();
      this.showSuccess(`Allocated ${requiredQty} units of ${inventoryId} to order ${orderNbr}`);
      
    } catch (error) {
      console.error('Error allocating inventory:', error);
      this.showError('Failed to allocate inventory: ' + error.message);
    }
  }

  async deallocateInventory(inventoryId, orderNbr) {
    try {
      console.log(`Deallocating ${inventoryId} from order ${orderNbr}`);
      
      await this.removeAllocation(inventoryId, orderNbr);
      await this.refresh();
      this.showSuccess(`Removed allocation of ${inventoryId} from order ${orderNbr}`);
      
    } catch (error) {
      console.error('Error deallocating inventory:', error);
      this.showError('Failed to deallocate inventory: ' + error.message);
    }
  }

  async autoAllocateAll() {
    try {
      console.log("Starting smart auto-allocation for all items...");
      
      const allocationRequirements = this.buildAllocationRequirements();
      let allocatedCount = 0;
      let partialCount = 0;
      
      // Smart allocation: Sort by priority (shipping date, then order number)
      const prioritizedRequirements = this.prioritizeAllocationRequirements(allocationRequirements);
      
      // Track available inventory as we allocate
      const inventoryTracker = new Map();
      
      // Initialize inventory tracker with current on-hand quantities
      this.parentComponent.inventoryItems.forEach(item => {
        inventoryTracker.set(item.InventoryID, {
          qtyOnHand: item.QtyOnHand || 0,
          totalAllocated: this.getTotalAllocatedForItem(item.InventoryID),
          availableQty: Math.max(0, (item.QtyOnHand || 0) - this.getTotalAllocatedForItem(item.InventoryID))
        });
      });
      
      console.log(`Processing ${prioritizedRequirements.length} requirements in priority order`);
      
      for (const req of prioritizedRequirements) {
        const inventoryInfo = inventoryTracker.get(req.InventoryID);
        
        if (!inventoryInfo) {
          console.warn(`No inventory info found for ${req.InventoryID}`);
          continue;
        }
        
        const currentAllocation = req.AllocatedQty || 0;
        const stillNeeded = req.RequiredQty - currentAllocation;
        
        if (stillNeeded <= 0) {
          console.log(`${req.InventoryID} for order ${req.OrderNbr} already fully allocated`);
          continue;
        }
        
        const canAllocateQty = Math.min(stillNeeded, inventoryInfo.availableQty);
        
        if (canAllocateQty > 0) {
          const finalAllocatedQty = currentAllocation + canAllocateQty;
          
          console.log(`Allocating ${canAllocateQty} units of ${req.InventoryID} to order ${req.OrderNbr} (Priority: ${req.Priority}, Ship Date: ${req.ShippingDateFormatted})`);
          
          const allocation = {
            id: `alloc-${req.InventoryID}-${req.OrderNbr}`,
            InventoryID: req.InventoryID,
            OrderNbr: req.OrderNbr,
            AllocatedQty: finalAllocatedQty,
            RequiredQty: req.RequiredQty,
            AllocationType: 'Auto',
            CreatedDate: new Date().toISOString(),
            Status: 'Active',
            ShippingDate: req.ShippingDate,
            Priority: req.Priority
          };
          
          await this.saveAllocation(allocation);
          
          // Update inventory tracker
          inventoryInfo.totalAllocated += canAllocateQty;
          inventoryInfo.availableQty -= canAllocateQty;
          
          if (finalAllocatedQty >= req.RequiredQty) {
            allocatedCount++;
            console.log(`✅ Fully allocated ${req.InventoryID} for order ${req.OrderNbr}`);
          } else {
            partialCount++;
            console.log(`⚠️ Partially allocated ${finalAllocatedQty}/${req.RequiredQty} of ${req.InventoryID} for order ${req.OrderNbr}`);
          }
        } else {
          console.log(`❌ No available inventory for ${req.InventoryID} (order ${req.OrderNbr})`);
        }
      }
      
      await this.refresh();
      
      let message = `Smart auto-allocation completed: ${allocatedCount} items fully allocated`;
      if (partialCount > 0) {
        message += `, ${partialCount} items partially allocated`;
      }
      
      this.showSuccess(message);
      
    } catch (error) {
      console.error('Error in smart auto-allocation:', error);
      this.showError('Failed to auto-allocate: ' + error.message);
    }
  }

  prioritizeAllocationRequirements(requirements) {
    console.log("🎯 Prioritizing allocation requirements by shipping date and urgency");
    
    return requirements
      .map(req => {
        // Add priority calculation
        const priority = this.calculateAllocationPriority(req);
        const shippingDateFormatted = this.formatShippingDateForSorting(req.ShippingDate);
        
        return {
          ...req,
          Priority: priority,
          ShippingDateFormatted: shippingDateFormatted,
          SortKey: `${priority}-${shippingDateFormatted}-${req.OrderNbr}`
        };
      })
      .sort((a, b) => {
        // Primary: Priority (lower number = higher priority)
        if (a.Priority !== b.Priority) {
          return a.Priority - b.Priority;
        }
        
        // Secondary: Shipping date (earlier dates first)
        const aDate = this.parseShippingDate(a.ShippingDate);
        const bDate = this.parseShippingDate(b.ShippingDate);
        
        if (aDate && bDate) {
          const timeDiff = aDate.getTime() - bDate.getTime();
          if (timeDiff !== 0) {
            return timeDiff;
          }
        } else if (aDate && !bDate) {
          return -1; // Orders with dates come before orders without dates
        } else if (!aDate && bDate) {
          return 1;
        }
        
        // Tertiary: Order number (alphabetical)
        return a.OrderNbr.localeCompare(b.OrderNbr);
      });
  }

  calculateAllocationPriority(req) {
    let priority = 5; // Default priority
    
    const shippingDate = this.parseShippingDate(req.ShippingDate);
    const now = new Date();
    
    if (shippingDate) {
      const daysUntilShipping = Math.ceil((shippingDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysUntilShipping < 0) {
        priority = 1; // Past due - highest priority
      } else if (daysUntilShipping <= 1) {
        priority = 2; // Ships tomorrow or today - very high priority
      } else if (daysUntilShipping <= 3) {
        priority = 3; // Ships within 3 days - high priority
      } else if (daysUntilShipping <= 7) {
        priority = 4; // Ships within a week - medium-high priority
      } else if (daysUntilShipping <= 14) {
        priority = 5; // Ships within 2 weeks - medium priority
      } else {
        priority = 6; // Ships in more than 2 weeks - lower priority
      }
    } else {
      priority = 7; // No shipping date - lowest priority
    }
    
    // Boost priority for items with shortage
    if (req.Shortage > 0) {
      priority = Math.max(1, priority - 1);
    }
    
    return priority;
  }

  parseShippingDate(shippingDate) {
    if (!shippingDate) return null;
    
    try {
      if (typeof shippingDate === 'object' && shippingDate.year) {
        // Acumatica date format: {year: 2024, month: 12, day: 15}
        return new Date(shippingDate.year, shippingDate.month - 1, shippingDate.day);
      } else if (typeof shippingDate === 'string') {
        return new Date(shippingDate);
      }
    } catch (error) {
      console.warn('Error parsing shipping date:', shippingDate, error);
    }
    
    return null;
  }

  formatShippingDateForSorting(shippingDate) {
    const date = this.parseShippingDate(shippingDate);
    if (!date) return '9999-12-31'; // Put items without dates at the end
    
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  }

  async clearAllAllocations() {
    try {
      if (!confirm('Are you sure you want to clear all allocations? This action cannot be undone.')) {
        return;
      }
      
      console.log("Clearing all allocations...");
      
      await this.clearAllAllocationsFromDB();
      await this.refresh();
      this.showSuccess('All allocations have been cleared');
      
    } catch (error) {
      console.error('Error clearing allocations:', error);
      this.showError('Failed to clear allocations: ' + error.message);
    }
  }

  generateDemand(inventoryId, shortage) {
    // Show demand generation modal
    this.showDemandGenerationModal(inventoryId, shortage);
  }

  async saveAllocation(allocation) {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName);
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        const transaction = db.transaction([this.allocationStoreName], "readwrite");
        const store = transaction.objectStore(this.allocationStoreName);
        
        store.put(allocation);
        
        transaction.oncomplete = () => {
          db.close();
          resolve();
        };
        
        transaction.onerror = (event) => {
          db.close();
          reject(event.target.error);
        };
      };
      
      request.onerror = (event) => {
        reject(event.target.error);
      };
    });
  }

  async removeAllocation(inventoryId, orderNbr) {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName);
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        const transaction = db.transaction([this.allocationStoreName], "readwrite");
        const store = transaction.objectStore(this.allocationStoreName);
        
        const allocationId = `alloc-${inventoryId}-${orderNbr}`;
        store.delete(allocationId);
        
        transaction.oncomplete = () => {
          db.close();
          resolve();
        };
        
        transaction.onerror = (event) => {
          db.close();
          reject(event.target.error);
        };
      };
      
      request.onerror = (event) => {
        reject(event.target.error);
      };
    });
  }

  async clearAllAllocationsFromDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName);
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        const transaction = db.transaction([this.allocationStoreName], "readwrite");
        const store = transaction.objectStore(this.allocationStoreName);
        
        store.clear();
        
        transaction.oncomplete = () => {
          db.close();
          resolve();
        };
        
        transaction.onerror = (event) => {
          db.close();
          reject(event.target.error);
        };
      };
      
      request.onerror = (event) => {
        reject(event.target.error);
      };
    });
  }

  showDemandGenerationModal(inventoryId, shortage) {
    const modalContent = `
      <div class="p-6">
        <div class="flex items-center justify-between pb-3 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            <i class="fas fa-plus-circle mr-2 text-blue-600"></i>
            Generate Demand for ${this.escapeHtml(inventoryId)}
          </h3>
          <button id="demand-close-button" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>

        <div class="mt-4">
          <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-4 mb-4">
            <div class="flex items-center text-red-800 dark:text-red-200">
              <i class="fas fa-exclamation-triangle mr-2"></i>
              <span class="text-sm">
                <strong>Shortage Detected:</strong> ${shortage} units needed for ${this.escapeHtml(inventoryId)}
              </span>
            </div>
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Recommended Action:
              </label>
              <div class="space-y-2">
                <label class="flex items-center">
                  <input type="radio" name="demand-action" value="purchase" checked class="mr-2">
                  <span class="text-sm">Generate Purchase Order for ${shortage} units</span>
                </label>
                <label class="flex items-center">
                  <input type="radio" name="demand-action" value="production" class="mr-2">
                  <span class="text-sm">Generate Production Order for ${shortage} units</span>
                </label>
                <label class="flex items-center">
                  <input type="radio" name="demand-action" value="transfer" class="mr-2">
                  <span class="text-sm">Request Warehouse Transfer</span>
                </label>
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Quantity to Order:
              </label>
              <input type="number" id="demand-quantity" value="${shortage}" min="1" 
                     class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Priority:
              </label>
              <select id="demand-priority" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
                <option value="normal">Normal</option>
                <option value="high">High</option>
                <option value="urgent" selected>Urgent</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Notes:
              </label>
              <textarea id="demand-notes" rows="3" placeholder="Additional notes for procurement team..."
                        class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"></textarea>
            </div>
          </div>
        </div>

        <div class="flex justify-end gap-2 pt-4 border-t border-gray-200 dark:border-gray-700 mt-6">
          <button id="demand-cancel-button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
            Cancel
          </button>
          <button id="demand-generate-button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            Generate Demand
          </button>
        </div>
      </div>
    `;

    // Create modal
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
    modalOverlay.id = 'demand-modal';
    
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md';
    modalContainer.innerHTML = modalContent;
    
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);
    
    // Setup event listeners
    const closeModal = () => {
      const modal = document.getElementById('demand-modal');
      if (modal && modal.parentNode) {
        modal.parentNode.removeChild(modal);
      }
    };
    
    document.getElementById('demand-close-button').addEventListener('click', closeModal);
    document.getElementById('demand-cancel-button').addEventListener('click', closeModal);
    
    document.getElementById('demand-generate-button').addEventListener('click', () => {
      const action = document.querySelector('input[name="demand-action"]:checked').value;
      const quantity = parseFloat(document.getElementById('demand-quantity').value);
      const priority = document.getElementById('demand-priority').value;
      const notes = document.getElementById('demand-notes').value;
      
      console.log('Demand generation request:', {
        inventoryId,
        action,
        quantity,
        priority,
        notes
      });
      
      // In a real implementation, this would create purchase/production orders
      this.showSuccess(`${action.charAt(0).toUpperCase() + action.slice(1)} order request generated for ${quantity} units of ${inventoryId}`);
      closeModal();
    });
    
    // Close on escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        closeModal();
      }
    }, { once: true });
    
    // Close on click outside
    modalOverlay.addEventListener('click', (event) => {
      if (event.target === modalOverlay) {
        closeModal();
      }
    });
  }

  // Utility methods
  getTotalRequirements() {
    return this.allocationResults.length;
  }

  getFullyAllocatedCount() {
    return this.allocationResults.filter(alloc => alloc.AllocationStatus === 'Fully Allocated').length;
  }

  getShortageCount() {
    return this.allocationResults.filter(alloc => alloc.QtyShort > 0).length;
  }

  getStatusClass(status) {
    switch (status?.toLowerCase()) {
      case 'fully allocated':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'partially allocated':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'can allocate':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'insufficient stock':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  }

  getPriorityClass(priority) {
    switch (priority) {
      case 1: // Past Due
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 2: // Urgent
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 3: // High
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 4: // Medium-High
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 5: // Medium
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      case 6: // Low
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 7: // No Date
        return 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  }

  getShippingDateClass(shippingDate) {
    const date = this.parseShippingDate(shippingDate);
    if (!date) return 'text-gray-500 dark:text-gray-400';
    
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return 'text-red-600 dark:text-red-400 font-medium'; // Past due
    } else if (diffDays <= 1) {
      return 'text-orange-600 dark:text-orange-400 font-medium'; // Due today/tomorrow
    } else if (diffDays <= 3) {
      return 'text-yellow-600 dark:text-yellow-400 font-medium'; // Due soon
    } else if (diffDays <= 7) {
      return 'text-blue-600 dark:text-blue-400'; // Due this week
    } else {
      return 'text-gray-600 dark:text-gray-400'; // Due later
    }
  }

  formatNumber(value) {
    if (value === null || value === undefined || isNaN(value)) return '0';
    return parseFloat(value).toFixed(2);
  }

  escapeHtml(text) {
    if (!text || text === null || text === undefined) return '';
    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  truncateText(text, maxLength = 40) {
    if (!text || text === null || text === undefined) return '';
    const str = text.toString();
    if (str.length <= maxLength) return str;
    return str.substring(0, maxLength) + '...';
  }

  showError(message) {
    console.error("Allocation error:", message);
    // Create temporary error message
    const errorElement = document.createElement('div');
    errorElement.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
    errorElement.innerHTML = `
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="ml-3">
          <span class="block sm:inline">${message}</span>
        </div>
        <div class="ml-auto pl-3">
          <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-red-500 hover:text-red-700">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(errorElement);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (errorElement.parentElement) {
        errorElement.parentElement.removeChild(errorElement);
      }
    }, 5000);
  }

  showSuccess(message) {
    console.log("Allocation success:", message);
    // Create temporary success message
    const successElement = document.createElement('div');
    successElement.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50';
    successElement.innerHTML = `
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="ml-3">
          <span class="block sm:inline">${message}</span>
        </div>
        <div class="ml-auto pl-3">
          <button onclick="this.parentElement.parentElement.parentElement.remove()" class="text-green-500 hover:text-green-700">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(successElement);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (successElement.parentElement) {
        successElement.parentElement.removeChild(successElement);
      }
    }, 5000);
  }
} 